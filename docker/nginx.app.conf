upstream php-service {
	# With php-cgi (or other tcp sockets):
	server 127.0.0.1:9000;

	# With php-fpm (or other unix sockets):
	#server unix:/run/php-fpm.sock;
}

server {
	listen 80 default_server;
	listen [::]:80 default_server;

	server_name _;
	root /app/public;
	index index.htm index.html index.php;
	absolute_redirect off;

	location / {
		# Check if a file or directory index file exists, else route it to index.php.
		try_files $uri $uri/ /index.php?q=$uri&$args;
	}

	# Pass the PHP scripts to PHP-FPM
	location ~ \.php$ {
		try_files $uri =404;
		fastcgi_pass php-service;
		include fastcgi.conf;

		# hide php version
		fastcgi_hide_header X-Powered-By;

		# enable SSE
		fastcgi_buffering off;
	}

	# Allow fpm ping and status from localhost
	location ~ ^/(fpm-status|fpm-ping)$ {
		fastcgi_pass php-service;
		include fastcgi.conf;
		access_log off;
		allow 127.0.0.1;
		deny all;

		# Check if the client's IP address is not 127.0.0.1 (not localhost)
		if ($remote_addr != 127.0.0.1) {
			return 404; # Return 404 Not Found for external clients.
		}
	}

	# Deny access to dotfiles for security
	location ~ /\. {
		log_not_found off;
		return 404; # deny all;
	}

	# custom error to app's 404 page
	error_page 404 /index.php/404;
}
