<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Register extends MY_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->load->library('auth/Authlib');
		$this->authlib->mustLogout();
	}

	public function index()
	{
		$data['url_form'] = site_url('auth/register/process_ajax');
		$data['url_terms'] = 'javascript:void(0)';
		$data['url_login'] = site_url('auth/login');
		$data['url_resend_activation'] = site_url('auth/activation/resend');
		// ------------------------------------------------------------------------
		$data['page_title'] = lang('Register');
		view('register', $data);
	}

	public function process_ajax()
	{
		$input_raw = $this->input->raw_input_stream;
		parse_str($input_raw, $input);

		$this->form_validation->set_data($input);
		$this->form_validation->set_rules('email', lang('email'), 'required');
		$this->form_validation->set_rules('password', lang('password'), 'required');
		$this->form_validation->set_rules('password_retype', lang('retype password'), 'required|matches[password]');
		$this->form_validation->set_rules('terms', lang('terms'), 'trim|required', [
			'required' => lang('You must agree with our terms').'.'
		]);
		if ($this->form_validation->run() == FALSE) {
			http_response_code(400);
			echo json([
				'message' => lang('Invalid Input'),
				'errors' => $this->form_validation->error_array(),
			]);
			die;
		}

		//input
		$email = trim($input['email']);
		$password = $input['password'];

		$this->db->trans_start();
		//get user
		$user = $this->Users_model->get_by_email($email)->row_array();
		if ($user) {
			http_response_code(403);
			echo json(['message' => lang('Email already registered')]);
			die;
		}

		//create user
		$user_id = $this->Users_model->create_user($email, $password);

		//generate activation code
		$code = $this->authlib->generate_activation_code($user_id);
		$code_hash = $code['hash'] ?? '';
		$code_key = $code['code'] ?? '';

		//save activation code
		$this->Users_model->save_user_activation_code($user_id, $code_hash);

		$complete = $this->db->trans_complete();
		if ($complete) {
			//send activation link
			$this->authlib->send_activation_link($email, $user_id, $code_key);

			//response
			http_response_code(201);
			echo json(['message' => lang('Please check your email to activate your account')]);
			die;
		}

		http_response_code(500);
		echo json(['message' => 'Internal Server Error [422]']);
	}

}

/* End of file Register.php */
/* Location: ./application/modules/auth/controllers/Register.php */