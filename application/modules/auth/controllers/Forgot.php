<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Forgot extends MY_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->load->library('auth/Authlib');
		$this->authlib->mustLogout();
	}

	// FORGOT PASSWORD
	// ------------------------------------------------------------------------
	public function index()
	{
		$data['url_form'] = site_url('auth/forgot/forgot_process_ajax');
		// ------------------------------------------------------------------------
		$data['page_title'] = lang('Forgot Password');
		view('forgot_password', $data);
	}

	public function forgot_process_ajax()
	{
		$input_raw = $this->input->raw_input_stream;
		parse_str($input_raw, $input);

		$this->form_validation->set_data($input);
		$this->form_validation->set_rules('email', lang('email'), 'trim|required|valid_email');
		if ($this->form_validation->run() == FALSE) {
			http_response_code(400);
			echo json([
				'message' => lang('Invalid Input'),
				'errors' => $this->form_validation->error_array(),
			]);
			die;
		}

		//input
		$email = trim($input['email']);

		$this->db->trans_start();
		//get user
		$user = $this->Users_model->get_by_email($email, TRUE)->row_array();
		if (!$user) {
			http_response_code(404);
			echo json(['message' => lang('Account Not Found')]);
			die;
		}

		//user data
		$user_id = $user['id'];
		$user_email = $user['email'];
		$user_is_activation = ($user['time_activation'] === NULL) ? FALSE : TRUE;

		//check user is activated
		if (!$user_is_activation) {
			http_response_code(403);
			echo json(['message' => lang('Account Not Activated')]);
			die;
		}

		//generate forgot code
		$forgot_code = $this->Users_model->generate_reset_password($user_id);

		$complete = $this->db->trans_complete();
		if ($complete) {
			//send recovery link
			$this->authlib->send_reset_password_link($email, $user_id, $forgot_code);

			//response
			echo json(['message' => lang('Reset Password link has been sent to your email')]);
			die;
		}

		http_response_code(500);
		echo json(['message' => 'Internal Server Error [422]']);
	}


	// PASSWORD RECOVERY
	// ------------------------------------------------------------------------
	public function recovery($user_id=null, $code_key=null)
	{
		//get user
		$user = $this->Users_model->get_by_id($user_id, TRUE)->row_array();
		if (!$user) {
			show_404();
		}

		//user data
		$user_id = $user['id'];
		$user_is_activation = ($user['time_activation'] === NULL) ? FALSE : TRUE;

		//check user must be activated
		if (!$user_is_activation) {
			show_404();
		}


		//validate forgot code
		// ------------------------------------------------------------------------
		$user_forgot = $user['password_forgot_code'] ?? NULL;
		$user_forgot_hash = preg_replace('/^[0-9]*_/i', '', $user_forgot);
		$forgot_time_expired = preg_replace('/\_.*/','',$user_forgot);

		//check expire
		$current_time = time();
		if ($current_time > $forgot_time_expired) {
			show_404();
		}

		//check token
		$token = $code_key.$forgot_time_expired;
		$valid = password_verify($token, $user_forgot_hash);
		if (!$valid) {
			show_404();
		}
		// ------------------------------------------------------------------------
		$data['url_login'] = site_url('auth/login');
		$data['user_id'] = $user_id;
		$data['code_key'] = $code_key;
		$data['url_form'] = site_url('auth/forgot/recovery_process_ajax');
		// ------------------------------------------------------------------------
		$data['page_title'] = lang('Password Recovery');
		view('forgot_password_recovery', $data);
	}

	public function recovery_process_ajax()
	{
		$input_raw = $this->input->raw_input_stream;
		parse_str($input_raw, $input);

		$this->form_validation->set_data($input);
		$this->form_validation->set_rules('user_id', 'user_id', 'trim|required');
		$this->form_validation->set_rules('code_key', 'code_key', 'trim|required');
		$this->form_validation->set_rules('password', lang('password'), 'trim|required|min_length[8]');
		$this->form_validation->set_rules('password_retype', lang('retype password'), 'trim|required|matches[password]');
		if ($this->form_validation->run() == FALSE) {
			http_response_code(400);
			echo json([
				'message' => lang('Invalid Input'),
				'errors' => $this->form_validation->error_array(),
			]);
			die;
		}

		//input
		$user_id = $input['user_id'];
		$code_key = $input['code_key'];
		$password = $input['password'];

		$this->db->trans_start();
		//get user
		$user = $this->Users_model->get_by_id($user_id, TRUE)->row_array();
		if (!$user) {
			http_response_code(404);
			echo json(['message' => lang('Account Not Found')]);
			die;
		}

		//user data
		$user_id = $user['id'];
		$user_email = $user['email'];
		$user_is_activation = ($user['time_activation'] === NULL) ? FALSE : TRUE;

		//check user must be activated
		if (!$user_is_activation) {
			http_response_code(403);
			echo json(['message' => lang('Account Not Activated')]);
			die;
		}


		//validate forgot code
		// ------------------------------------------------------------------------
		$user_forgot = $user['password_forgot_code'] ?? NULL;
		$user_forgot_hash = preg_replace('/^[0-9]*_/i', '', $user_forgot);
		$forgot_time_expired = preg_replace('/\_.*/','',$user_forgot);

		//check expire
		$current_time = time();
		if ($current_time > $forgot_time_expired) {
			http_response_code(410);
			echo json(['message' => lang('Reset Password Expired')]);
			die;
		}

		//check token
		$token = $code_key.$forgot_time_expired;
		$valid = password_verify($token, $user_forgot_hash);
		if (!$valid) {
			http_response_code(401);
			echo json(['message' => lang('Invalid Token')]);
			die;
		}
		// ------------------------------------------------------------------------

		//update password
		$this->Users_model->update_password($user_id, $password);

		$complete = $this->db->trans_complete();
		if ($complete) {
			//send notif password change
			$this->authlib->send_reset_password_success($user_email);

			//response
			echo json(['message' => lang('Password Changed Successfully')]);
			die;
		}

		http_response_code(500);
		echo json(['message' => 'Internal Server Error [422]']);
	}

}

/* End of file Forgot.php */
/* Location: ./application/modules/auth/controllers/Forgot.php */