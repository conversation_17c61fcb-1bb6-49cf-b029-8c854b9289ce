<?php

/**
 * Welcome to Luthier-CI!
 *
 * This is your main route file. Put all your HTTP-Based routes here using the static
 * Route class methods
 *
 * Examples:
 *
 *    Route::get('foo', 'bar@baz');
 *      -> $route['foo']['GET'] = 'bar/baz';
 *
 *    Route::post('bar', 'baz@fobie', [ 'namespace' => 'cats' ]);
 *      -> $route['bar']['POST'] = 'cats/baz/foobie';
 *
 *    Route::get('blog/{slug}', 'blog@post');
 *      -> $route['blog/(:any)'] = 'blog/post/$1'
 */

Route::get('/', 'Welcome@index');

Route::set('404_override', function(){
    show_404();
});

Route::set('translate_uri_dashes',FALSE);
// ------------------------------------------------------------------------
Route::get('/logout', 'auth/Logout@index');
Route::get('/login', 'auth/Login@index');
Route::get('/register', 'auth/Register@index');
Route::get('/auth/login', 'auth/Login@index');
Route::post('/auth/login/process_ajax', 'auth/Login@process_ajax');
Route::get('/auth/register', 'auth/Register@index');
Route::post('/auth/register/process_ajax', 'auth/Register@process_ajax');
Route::get('auth/logout', 'auth/Logout@index');


Route::group('/user', ['middleware' => ['AuthMustLoggedin']], function()
{
    Route::get('dashboard', 'user/Dashboard@index');
    Route::get('members', 'user/Members@index');
    Route::get('photos', 'user/Photos@index');
});

Route::group('/user/settings', function()
{
    Route::get('/', 'user/Settings@index', ['middleware' => ['AuthMustLoggedin']]);
    Route::patch('/ajax_change_email', 'user/Settings@ajax_change_email', ['middleware' => ['AuthMustLoggedin']]);
    Route::patch('/ajax_change_password', 'user/Settings@ajax_change_password', ['middleware' => ['AuthMustLoggedin']]);
});


Route::get('simfoni/sync/chain', 'simfoni/Sync@chain');
Route::get('simfoni/sync/status', 'simfoni/Sync@status');
Route::get('simfoni/sync/progress', 'simfoni/Sync@progress');
Route::get('simfoni/sync/member', 'simfoni/Sync@member');
Route::get('simfoni/sync/photo', 'simfoni/Sync@photo');
Route::get('similarity', 'similarity/Similarity@index');
Route::post('similarity', 'similarity/Similarity@retrieve');
Route::get('similarity/progress', 'simfoni/Sync@progress');
Route::get('similarity/status', 'similarity/Similarity@status');


Route::group('ajax', ['middleware' => ['AuthMustLoggedinAPI']], function()
{
    Route::get('members/datatable', 'user/Members@ajax_datatable');
    Route::group('photos', function()
    {
        Route::get('/datatable', 'user/Photos@ajax_datatable');
        Route::get('/{any:simfoni_photo_id}', 'user/Photos@get/$1');
    });
});