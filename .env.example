# app domain trailing with slash. ex: http://example.com/
BASE_URL=

# CodeIgniter
CI_ENV=					# development, testing, production
CI_ENCRYPTION_KEY=		# any string for encryption
CI_MIGRATION=			# false, true
CI_MIGRATION_VERSION=	# false, version number, latest
CI_STORAGE_SERVER=		# local, cloudinary, google
CI_STORAGE_PATH=		# document root / public path for storage with trailing slash, ex: storage/

# Database
DB_HOST=
DB_NAME=
DB_USER=
DB_PASS=

# EMAIL SMTP
MAIL_SENDER_NAME=
MAIL_HOST=				# ssl://smtp.zoho.com, ssl://smtp.gmail.com, ssl://smtp.googlemail.com
MAIL_PORT=				# 465
MAIL_USER=
MAIL_PASS=


# API
API_SIMFONI_URL=https://api.simfoni-fpsi.web.id/
API_SIMFONI_KEY=

API_SIMILARITY_URL=https://api-similarity-zx53qegsda-as.a.run.app/
API_SIMILARITY_KEY=