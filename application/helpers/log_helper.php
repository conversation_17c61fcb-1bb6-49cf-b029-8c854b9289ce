<?php
if (!function_exists('print_log')) {
	function print_log($message, $type='log')
	{
		$datetime = date('Y-m-d H:i:s');
		file_put_contents("php://stderr", $datetime . ' ' .strtoupper($type) .': '. $message . PHP_EOL);
	}
}

if (!function_exists('run_background')) {
	function run_background($command)
	{
		print_log('Run Background :: '.$command);
		shell_exec($command . " > /dev/null 2>/dev/null &");
	}

	// function run_background($command, $outputFile = '/dev/null') {
	// 	$processId = shell_exec(sprintf(
	// 		'%s > %s 2>&1 & echo $!',
	// 		$command,
	// 		$outputFile
	// 	));

	// 	// print_r("processID of process in background is: " . $processId);
	// }
}