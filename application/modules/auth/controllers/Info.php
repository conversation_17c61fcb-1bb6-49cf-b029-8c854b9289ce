<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Info extends MY_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->load->library('session');
	}

	public function index()
	{
		echo '<pre>';
		print_r($this->session->all_userdata());
		print_r($this->input->cookie());
		echo '</pre>';
	}

}

/* End of file Info.php */
/* Location: ./application/modules/auth/controllers/Info.php */