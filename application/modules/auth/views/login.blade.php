@extends('layouts/public')
@section('page_title', $page_title)
@section('content')
<div class="login-box m-auto pt-4 pb-5">
	<div class="login-logo">
		<a href="javascript:void(0)">
			<img src="<?=base_url('logo-fpsi.png') ?>" class="img-fluid" style="max-height: 100px;">
			<br>
			Similarity Photo Checker
		</a>
	</div>
	<div class="card">
		<div class="card-body login-card-body">
			<!-- <p class="login-box-msg">Sign in to start your session</p> -->
			<div class="mt-2"></div>

			<form id="myform" action="<?=$url_form ?>" method="post">
				<div class="input-group">
					<input type="email" name="email" class="form-control" placeholder="<?=lang('Email') ?>">
					<div class="input-group-append">
						<div class="input-group-text">
							<span class="fas fa-envelope"></span>
						</div>
					</div>
				</div>
				<div class="text-danger mb-3" data-error="email"></div>

				<div class="input-group">
					<input type="password" name="password" class="form-control" placeholder="<?=lang('Password') ?>">
					<div class="input-group-append">
						<div class="input-group-text">
							<span class="fas fa-lock"></span>
						</div>
					</div>
				</div>
				<div class="text-danger mb-3" data-error="password"></div>

				<div class="row">
					<div class="col-8">
						<div class="icheck-primary">
							<input type="checkbox" id="remember" name="remember">
							<label for="remember">
								<?=lang('Remember Me') ?>
							</label>
						</div>
					</div>
					<!-- /.col -->
					<div class="col-4">
						<button type="submit" class="btn btn-primary btn-block"><?=lang('Sign In') ?></button>
					</div>
					<!-- /.col -->
				</div>
			</form>

			<p class="mb-1">
				<a href="<?=$url_forgot_password ?>"><?=lang('Forgot Password') ?></a>
			</p>
			<p class="mb-0">
				<a href="<?=$url_register ?>" class="text-center"><?=lang('Register New Account') ?></a>
			</p>
		</div>
		<!-- /.login-card-body -->
	</div>
	<!-- /.card -->
</div>
<!-- /.login-box -->
@endsection
@section('js')
<script>
$('#myform').submit(function(e) {
	e.preventDefault();

	let form = $(this);
	let url = form.attr('action');
	let method = form.attr('method');
	let btn = form.find('[type=submit]');
	let errors = form.find('[data-error]');

	$.ajax({
		url: url,
		type: method,
		data: form.serialize(),
		dataType: 'json',
		beforeSend: function() {
			//set loading
			btn.prop('disabled', true).button('loading');

			//reset form error
			form.find('[name]').removeClass('is-invalid');
			errors.text('');
		},
		success: function(response, status, xhr) {
			document.write(response.message);
			window.location = '<?=site_url() ?>';
		},
		error: function(xhr, status, error) {
			let response = xhr.responseJSON;
			let response_status = xhr.status;

			//handle server error
			if (response_status >= 500) {
				Swal.fire(`<?=lang('Please try again') ?>!`,  `${error} [${response_status}]`, `error`);
				return;
			}

			//handle unwanted response
			if (response == undefined) {
				Swal.fire(`<?=lang('Oops') ?>..!`, `<?=lang('Invalid Response') ?> [${response_status}]`, `error`);
				return;
			}

			//handle error list
			Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
			let errors = response.errors;
			if (errors != undefined) {
				Object.keys(errors).forEach(function(k) {
					form.find('[name='+k+']').addClass('is-invalid');
					form.find('[data-error='+k+']').addClass('text-danger text-xs').text(errors[k]);
				})
			}
		}
	}).always(function() {
		btn.prop('disabled', false).button('reset');
	});
})
</script>
@endsection