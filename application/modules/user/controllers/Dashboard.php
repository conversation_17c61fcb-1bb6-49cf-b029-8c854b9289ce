<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends MY_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->load->library('auth/Authlib');
		$this->authlib->mustLogin();
	}

	public function index()
	{
		$total_member = $this->db->select('COUNT(id) AS total')->get('members')->row_array()['total'] ?? 0;
		$total_photo = $this->db->select('COUNT(id) AS total')->get('photos')->row_array()['total'] ?? 0;

		$time_sync_member = $this->db->order_by('time_updated', 'DESC')->limit(1)->get('members')->row_array()['time_updated'] ?? 0;
		$time_sync_photo = $this->db->order_by('time_updated', 'DESC')->limit(1)->get('photos')->row_array()['time_updated'] ?? 0;

		$data['total_member'] = $total_member;
		$data['total_photo'] = $total_photo;
		$data['time_sync_member'] = $time_sync_member;
		$data['time_sync_photo'] = $time_sync_photo;
		$data['page_title'] = lang('Dashboard');
		view('dashboard', $data);
	}

}

/* End of file Dashboard.php */
/* Location: ./application/modules/user/controllers/Dashboard.php */