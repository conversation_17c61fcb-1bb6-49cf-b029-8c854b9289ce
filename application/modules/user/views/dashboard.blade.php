@extends('layouts.app')
@section('page_title', $page_title)
@section('css')
<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
<style>
.progress-text {
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none; /* biar nggak ganggu interaksi */
}

</style>
@endsection
@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
	<div class="container-fluid">
		<div class="row mb-2">
			<div class="col-sm-12">
				<h1 class="m-0"><?=lang('Dashboard') ?></h1>
			</div><!-- /.col -->
		</div><!-- /.row -->
	</div><!-- /.container-fluid -->
</section>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-6 col-sm-6">
				<div class="small-box bg-info">
					<div class="inner">
						<h3><?=number_format($total_member) ?></h3>
						<p>Total Sync Member</p>
					</div>
					<div class="icon">
						<i class="ion ion-ios-people"></i>
					</div>
					<a href="javascript:void()" class="small-box-footer text-left pl-2">Last Sync: <span data-time="<?=$time_sync_member ?>"><?=$time_sync_member ?></span></a>
				</div>
			</div>
			<div class="col-md-6 col-sm-6">
				<div class="small-box bg-success">
					<div class="inner">
						<h3><?=number_format($total_photo) ?></h3>
						<p>Total Sync Photo</p>
					</div>
					<div class="icon">
						<i class="ion ion-images"></i>
					</div>
					<a href="javascript:void(0)" class="small-box-footer text-left pl-2">Last Sync: <span data-time="<?=$time_sync_photo ?>"><?=$time_sync_photo ?></span></a>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-12">
				<a href="javascript:syncSimfoniChain()" class="btn btn-block bg-indigo btn-flat">Synchronize SIMFONI Database</a>
				<!--
				<a href="javascript:syncSIMFONI()" class="btn btn-block bg-indigo btn-flat">Synchronize SIMFONI Database [stage version]</a>
				<a href="javascript:syncMember()" class="btn btn-block bg-indigo btn-flat">Synchronize SIMFONI Member</a>
				<a href="javascript:syncPhoto()" class="btn btn-block bg-indigo btn-flat">Synchronize SIMFONI Member's Photo</a>
				-->
			</div>
			<div class="col-12">
				<div class="mt-2">
					<div id="progress" class="progress position-relative d-none" style="height: 35px;">
						<div id="progress-bar" class="progress-bar bg-primary progress-bar-striped progress-bar-animated" style="width: 0%;"></div>
						<span id="progress-text" class="progress-text position-absolute w-100 text-center">Not Running</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
@section('js')
<script>
$(document).ready(function () {
	updateProgressFromStatus();
});
</script>
<script>
function updateProgressFromStatus() {
  $.getJSON('<?=site_url("simfoni/sync/status")?>', function(status) {
    const stages = ['member', 'photo'];
    let foundInProgress = false;

    for (let i = 0; i < stages.length; i++) {
      let stage = stages[i];
      let data = status.data[stage];

      if (data.in_progress && data.progress_url) {
        foundInProgress = true;
        $('#progress').removeClass('d-none');
        let stage_text = stage.charAt(0).toUpperCase() + stage.slice(1);
        let label = `[${i+1}/${stages.length}] Sync ${stage_text} `;

        sseProgress(data.progress_url, label, function () {
          // Kalau ini stage terakhir
          if (i === stages.length - 1) {
            $('#progress-text').text('All sync completed! ✅');
            $('#progress-bar').width('100%');

            Swal.fire('Completed', 'SIMFONI synchronization done', 'success').then(function() {
            	document.write('please wait..');
            	window.location = window.location.href;
            })
          } else {
            // Kalau bukan terakhir, tampilkan "waiting..."
            $('#progress-text').text(label + 'Completed, waiting for next stage...');
            setTimeout(() => {
              updateProgressFromStatus();
            }, 3000); // Delay 3 detik, bisa disesuaikan
          }
        });

        break; // Stop loop, hanya satu stage aktif dalam satu waktu
      }
    }

    if (!foundInProgress) {
      $('#progress-bar').width('0%');
      $('#progress-text').text('No Sync Running');
    }
  }).fail(function() {
    $('#progress-text').text('Failed to get sync status');
  });
}
function syncSimfoniChain() {
	Swal.fire({
		title: 'Sync SIMFONI Database?',
		text: 'We will synchronize SIMFONI Database to the system',
		icon: 'question',
		showCancelButton: true,
		focusCancel: true,
		cancelButtonColor: '#d33',
		cancelButtonText: '<?=lang('Cancel') ?>',
		confirmButtonText: '<?=lang('Synchronize') ?>',
		confirmButtonColor: '#3085d6',
		showLoaderOnConfirm: true,
		allowOutsideClick: () => !Swal.isLoading(),
		preConfirm: function() {
			return new Promise(function(resolve) {
				$.ajax({
					url: '<?= site_url("simfoni/sync/chain") ?>',
					method: 'GET',
					success: function(response, status, xhr) {
						const statusUrl = response.data.progress_url;
						$('#progress').removeClass('d-none');
						$('#progress-bar').width('0%');
						$('#progress-text').text('Waiting to start...');
						setTimeout(updateProgressFromStatus, 5000);
					},
					error: function(xhr, status, error) {
						let response = xhr.responseJSON;
						let response_status = xhr.status;

						//handle no internet
						if (response_status == 0) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Your connection might be unstable, please try again later.') ?> ['+response_status+']', 'warning');
							return;
						}

						//handle server error
						if (response_status >= 500) {
							Swal.fire(error+' ['+response_status+']', '<?=lang('Please try again or contact our support') ?>!', 'error');
							return;
						}

						//handle server invalid response
						if (response == undefined) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Server Error') ?> ['+xhr.status+']', 'error');
							return;
						}

						//handle error response
						Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
					}
				}).always(function() {
					resolve();
				});
			});
			//end promise
		}
	})
}
</script>

<script>
function syncStep(url, label, onComplete) {
	$.ajax({
		type: 'GET',
		url: url,
		dataType: 'json',
		success: function(response, status, xhr) {
			Swal.close();
			$('#progress').removeClass('d-none');
			$('#progress').find('#progress-bar').width('0%');
			$('#progress').find('#progress-text').text(label + '0%');

			let data = response.data;
			let progress_url = data.progress_url;

			let eventSource = new EventSource(progress_url);

			eventSource.onmessage = function(event) {
				console.log(event.data);
				let res = JSON.parse(event.data);

				if (res.code === 'error') {
					eventSource.close();
					Swal.fire('<?=lang('Oops') ?>..!', res.message, 'error');
					return;
				}

				let percent = res.data.percentage;
				let percent_str = percent + '%';
				$('#progress-bar').width(percent_str);
				$('#progress-text').text(label + percent_str);

				if (percent >= 100) {
					eventSource.close();
					if (typeof onComplete === 'function') {
						onComplete();
					}
				}
			};

			eventSource.onerror = function(err) {
				console.error("SSE connection error:", err);
				eventSource.close();
			};
		},
		error: function(xhr, status, error) {
			let response = xhr.responseJSON;
			let response_status = xhr.status;

			//handle no internet
			if (response_status == 0) {
				Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Your connection might be unstable, please try again later.') ?> ['+response_status+']', 'warning');
				return;
			}

			//handle server error
			if (response_status >= 500) {
				Swal.fire(error+' ['+response_status+']', '<?=lang('Please try again or contact our support') ?>!', 'error');
				return;
			}

			//handle server invalid response
			if (response == undefined) {
				Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Server Error') ?> ['+xhr.status+']', 'error');
				return;
			}

			//handle error response
			Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
		}
	});
}
function syncSIMFONI() {
	Swal.fire({
		title: 'Sync with SIMFONI Database?',
		text: 'We will synchronize SIMFONI Database to the system',
		icon: 'warning',
		showCancelButton: true,
		focusCancel: true,
		cancelButtonColor: '#d33',
		cancelButtonText: '<?=lang('Cancel') ?>',
		confirmButtonText: '<?=lang('Synchronize') ?>',
		confirmButtonColor: '#3085d6',
		showLoaderOnConfirm: true,
		allowOutsideClick: () => !Swal.isLoading(),
		preConfirm: function() {
			return new Promise(function(resolve) {
				let memberURL = '<?=site_url('simfoni/sync/member') ?>';
				let photoURL = '<?=site_url('simfoni/sync/photo') ?>';

				// Step 1: Sync Member
				syncStep(memberURL, '[1/2] Sync Member ', function() {
					// Step 2: Sync Photo
					syncStep(photoURL, '[2/2] Sync Photo ', function() {
						Swal.fire('<?=lang("Success") ?>', 'All sync completed!', 'success');
						resolve();
					});
				});
			});
		}
	});
}
</script>

<script>
function sseProgress(progress_url, label, onComplete) {
	// Create new event, the server script is sse
	let eventSource = new EventSource(progress_url);

	// Event when receiving a message from the server
	let progress = 0;
	eventSource.onmessage = function(event) {
		console.log(event.data);

		let response = JSON.parse(event.data);
		if (response.code == 'error') {
			eventSource.close();

			Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
			return;
		}

		//set progress percentage
		progress = response.data.percentage;
		let progress_percentage = progress + '%';

		//set progress bar
		$('#progress-bar').width(progress_percentage);
		$('#progress-text').text(label + progress_percentage);
		if (progress >= 100) {
			console.log('progress '+label+' done');
			eventSource.close();

			// Jalankan callback setelah SSE selesai
			if (typeof onComplete === 'function') {
				onComplete();
			}
		}
	}
}
function syncMember() {
	let url = '<?=site_url('simfoni/sync/member') ?>';
	Swal.fire({
		title: 'Sync SIMFONI Member?',
		text: 'We will synchronize SIMFONI Member data to the system',
		icon: 'warning',
		showCancelButton: true,
		focusCancel: true,
		cancelButtonColor: '#d33',
		cancelButtonText: '<?=lang('Cancel') ?>',
		confirmButtonText: '<?=lang('Synchronize') ?>',
		confirmButtonColor: '#3085d6',
		showLoaderOnConfirm: true,
		allowOutsideClick: () => !Swal.isLoading(),
		preConfirm: function() {
			return new Promise(function(resolve, reject) {
				$.ajax({
					type: 'GET',
					url: url,
					dataType: 'json',
					success: function(response, status, xhr) {
						Swal.fire('<?=lang('Success') ?>', response.message, 'success');
						$('#progress').removeClass('d-none');
						$('#progress').find('#progress-bar').width('0%');
						$('#progress').find('#progress-text').text('Sync Member 0%');

						//set progress bar
						let data = response.data;
						sseProgress(data.progress_url, 'Sync Member ');
					},
					error: function(xhr, status, error) {
						let response = xhr.responseJSON;
						let response_status = xhr.status;

						//handle no internet
						if (response_status == 0) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Your connection might be unstable, please try again later.') ?> ['+response_status+']', 'warning');
							return;
						}

						//handle server error
						if (response_status >= 500) {
							Swal.fire(error+' ['+response_status+']', '<?=lang('Please try again or contact our support') ?>!', 'error');
							return;
						}

						//handle server invalid response
						if (response == undefined) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Server Error') ?> ['+xhr.status+']', 'error');
							return;
						}

						//handle error response
						Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
					}
				}).always(function() {
					resolve();
				})
				//ajax end
			})
			//promise end
		}
	})
}
function syncPhoto() {
	let url = '<?=site_url('simfoni/sync/photo') ?>';
	Swal.fire({
		title: "Sync SIMFONI Member's Photo?",
		text: 'We will synchronize SIMFONI Database to the system',
		icon: 'warning',
		showCancelButton: true,
		focusCancel: true,
		cancelButtonColor: '#d33',
		cancelButtonText: '<?=lang('Cancel') ?>',
		confirmButtonText: '<?=lang('Synchronize') ?>',
		confirmButtonColor: '#3085d6',
		showLoaderOnConfirm: true,
		allowOutsideClick: () => !Swal.isLoading(),
		preConfirm: function() {
			return new Promise(function(resolve, reject) {
				$.ajax({
					type: 'GET',
					url: url,
					dataType: 'json',
					success: function(response, status, xhr) {
						Swal.fire('<?=lang('Success') ?>', response.message, 'success');
						$('#progress').removeClass('d-none');
						$('#progress').find('#progress-bar').width('0%');
						$('#progress').find('#progress-text').text('Sync Photo 0%');

						//set progress bar
						let data = response.data;
						sseProgress(data.progress_url, 'Sync Photo ');
					},
					error: function(xhr, status, error) {
						let response = xhr.responseJSON;
						let response_status = xhr.status;

						//handle no internet
						if (response_status == 0) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Your connection might be unstable, please try again later.') ?> ['+response_status+']', 'warning');
							return;
						}

						//handle server error
						if (response_status >= 500) {
							Swal.fire(error+' ['+response_status+']', '<?=lang('Please try again or contact our support') ?>!', 'error');
							return;
						}

						//handle server invalid response
						if (response == undefined) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Server Error') ?> ['+xhr.status+']', 'error');
							return;
						}

						//handle error response
						Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
					}
				}).always(function() {
					resolve();
				})
				//ajax end
			})
			//promise end
		}
	})
}
</script>
@endsection