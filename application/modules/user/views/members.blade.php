@extends('layouts.app')
@section('page_title', $page_title)
@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
	<div class="container-fluid">
		<div class="row mb-2">
			<div class="col-sm-12">
				<h1 class="m-0"><?=lang('Member List') ?></h1>
			</div><!-- /.col -->
		</div><!-- /.row -->
	</div><!-- /.container-fluid -->
</section>
<!-- /.content-header -->

<!-- Main content -->
<section class="content" id="section-page">
	<div class="container-fluid">
		<!-- Default box -->
		<div class="card">
			<div class="card-body">
				<div class="table-responsive">
					<table id="mytable" class="table table-bordered table-hover table-sm text-nowrap" width="100%">
						<thead>
							<tr>
								<th width="50px">No</th>
								<th><?=lang('FP Number') ?></th>
								<th><?=lang('Name') ?></th>
								<th><?=lang('Honors') ?></th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
			<!-- /.card-body -->
		</div>
		<!-- /.card -->
	</div>
	<!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection

@section('js')
<script>
let url_ajax = '<?=$url_ajax ?>';
let tb = '#mytable';
let tb_url = url_ajax + '/datatable';
let mytable = $(tb).DataTable({
	// dom: `<"row" <"col-sm-12 col-md-6" l><"col-sm-12 col-md-6" <"toolbar"> f>>
	// 	  <"row" <"col-sm-12" tr>>
	// 	  <"row" <"col-sm-12 col-md-5" i><"col-sm-12 col-md-7" p>>`,
	processing: false,
	serverSide: true,
	ajax: {
		url: tb_url,
		type: 'GET',
		data: function(d) {
			return $.extend({}, d, {})
		},
		dataType: 'json',
		beforeSend: function() {
			$('#section-page').find('.card').append('<div class="overlay"><i class="fas fa-2x fa-sync-alt fa-spin"></i></div>');
		},
		error: function(xhr, status, error) {
			$('#section-page').find('.overlay').remove();
			alert(error);
		},
		dataSrc: function(json) {
			$('#section-page').find('.overlay').remove();
			return json.data;
		},
	},
	columnDefs: [{
		targets: '_all',
		render: $.fn.dataTable.render.text()
	}],
	columns: [
		{
			data: 'id',
			searchable: false,
			render: function(data, type, row, meta) {
				return meta.row + meta.settings._iDisplayStart + 1;
			}
		},
		{ data: 'simfoni_id' },
		{ data: 'name' },
		{
			data: 'honors',
			searchable: false,
		}
	],
	initComplete: function() {
		var api = this.api();
		$(tb + '_filter input').off('.DT').on('keyup.DT', function(e) {
			if (e.keyCode == 13) {
				api.search(this.value).draw();
			}
		});

		let searchParams = new URLSearchParams(window.location.search);
		let param = searchParams.get('search');
		console.log('search: '+param)
		if (param != '' && param != null) {
			var e = jQuery.Event("keyup");
			e.which = 13; //choose the one you want
			e.keyCode = 13;
			$(tb+'_filter input').val(param);
			$(tb+'_filter input').trigger(e);
		}
	},
	<?php /*https://datatables.net/forums/discussion/36595/change-the-row-color-based-on-column-data*/ ?>
	createdRow: function(row, data, dataIndex) {
		//check similarity
		if (data.similarity != null && data.similarity != 'checked') {
			$(row).css('background','#ffc107').attr('title','<?=translate('Similarity Found') ?>');
		}
		if (data.disqualification != null && data.disqualification != '') {
			$(row).css('background','red').attr('title','<?=translate('Disqualified') ?>: '+data.disqualification);
		}
	}
});
</script>
@endsection