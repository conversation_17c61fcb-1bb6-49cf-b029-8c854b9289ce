<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Similarity extends MY_Controller {

	protected $similarity_url;
	protected $similarity_key;

	public function __construct()
	{
		parent::__construct();
		$api_url = $_SERVER['API_SIMILARITY_URL'] ?? '';
		$similarity_url = rtrim($api_url,'/'); //remove slash suffix
		$similarity_url = $similarity_url . '/v1/similarity/';
		$this->similarity_url = $similarity_url;
		$this->similarity_key = $_SERVER['API_SIMILARITY_KEY'] ?? '';

		session_write_close();
	}

	public function index()
	{
		$request_id = generateUUIDv4();

		//prevent duplicate request
		// ------------------------------------------------------------------------
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'similarity'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 30; // allow after 30 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = $progress['id'] ?? '';
				$progressID = remove_suffix($progressID, '.progress');
				http_response_code(403);
				echo json([
					'message' => 'Checking similarity process is still running',
					'data' => [
						'progress_url' => site_url('similarity/progress').'?id='.$progressID,
					]
				]);
				die;
			}
		}



		$list_photos = $this->db->get('photos')->result_array();
		$total_photo = count($list_photos);
		$similarity_data_split = 50;
		$total_data_split = ceil($total_photo / $similarity_data_split);

		echo json([
			'message' => 'Check '.number_format($total_photo).' Similarity photos',
			'data' => [
				'progress_url' => site_url('similarity/progress').'?id='.$request_id,
			]
		]);
		fastcgi_finish_request();

		print_log('['.$request_id.'] Send Similarity');
		# book progress
		$this->db->insert('ci_sessions', [
			'id' => $request_id.'.progress',
			'ip_address' => 'similarity',
			'timestamp' => time(),
			'data' => 0,
		]);

		# prepare data
		$image_urls_data = [];
		$queue_index = 0;
		$queue_data_total = 0;
		foreach ($list_photos as $index => $photo) {
			$simfoni_photo_id = $photo['simfoni_id'];
			$simfoni_photo_url = ($photo['image_url']);


			//prepare compare
			$list_compare = [];
			/*
			foreach ($list_photos as $compare_photo) {
				//skip same ID
				if ($simfoni_photo_id == $compare_photo['simfoni_id']) {
					continue;
				}

				//append data list_compare
				$list_compare[] = [
					'url' => ($compare_photo['image_url']),
					'meta_data' => [
						'photo_id' => $compare_photo['id'],
						'simfoni_photo_id' => $compare_photo['simfoni_id'],
					]
				];
			}
			*/


			//append data to queue
			$image_urls_data[$queue_index][] = [
				'url' => $simfoni_photo_url,
				'meta_data' => [
					'request_id' => $request_id,
					'queue_no' => ($queue_index+1),
					'queue_total' => $total_data_split,
					'photo_id' => $photo['id'],
					'simfoni_photo_id' => $simfoni_photo_id,
				],
				'compare' => $list_compare,
			];


			//set queue for similarity
			$queue_data_total++;
			if ($queue_data_total >= $similarity_data_split) {
				$queue_data_total=0;
				$queue_index++;
			}
		}


		$total_queue = ($queue_index+1);
		$this->db->insert('logs', [
				'request_id' => $request_id,
				'type' => 'similarity',
				'message' => 'START Similarity ('.number_format($total_photo).' photos) with '.number_format($total_queue).' queue',
				'data' => null,
				'time_created' => time(),
			]);



		//send similarity data to API service
		$similarity_score_percentage = 0.5;
		$webhook_url = current_url();
		foreach ($image_urls_data as $queue_index2 => $image_urls) {
			$bodyData = [
				'min_score' => $similarity_score_percentage,
				'webhook' => [
					'url' => $webhook_url,
				],
				'image_urls' => $image_urls,
			];
			$body = json_encode($bodyData);


			//v1
			// $body_cli = escapeshellarg($body);
			$body_cli = ($body);
			$exec = "curl --location --request POST '".$this->similarity_url."' ";
			$exec .= "--header 'public-key: ".$this->similarity_key."' ";
			$exec .= "--header 'Content-Type: application/json' ";
			$exec .= "--data-raw '".$body_cli."' ";
			run_background($exec);

			//v2
			// $curl = curl_init();
			// curl_setopt_array($curl, [
			// 	CURLOPT_URL => $this->similarity_url,
			// 	CURLOPT_RETURNTRANSFER => true,
			// 	CURLOPT_POST => true,
			// 	CURLOPT_HTTPHEADER => [
			// 		"public-key: $this->similarity_key",
			// 		"Content-Type: application/json"
			// 	],
			// 	CURLOPT_POSTFIELDS => $body,
			// ]);
			// $info = curl_getinfo($curl);
			// $response = curl_exec($curl);
			// if (curl_errno($curl)) {
			// // 	$object_log['data_after'] = curl_error($curl);
			// // } else {
			// // 	$object_log['data_after'] = $response;

			// 	print_log('['.$request_id.'] Send Similarity FAILED -> '.curl_error($curl));
			// }
			// curl_close($curl);

			print_log('['.$request_id.'] Send similarity queue ('.($queue_index2+1).'/'.$total_queue.') data');

			//sleep
			sleep(1);
		}


		print_log('['.$request_id.'] Send Similarity DONE with '.number_format($total_queue).' queue');
		echo 'done';
	}

	public function retrieve()
	{
		//retrieve input
		$body = $this->input->raw_input_stream;

		$data = json_decode($body, true);
		$result = $data['result'] ?? [];
		$request_id = $result[0]['meta_data']['request_id'] ?? NULL;
		$queue_no = $result[0]['meta_data']['queue_no'] ?? NULL;
		$total_data_split = $result[0]['meta_data']['queue_total'] ?? NULL;
		print_log('request coming...'.$request_id.' :: '.$queue_no);

		# add log
		$this->db->insert('logs', [
			'request_id' => $request_id,
			'type' => 'similarity_response',
			'message' => 'Retrieve Webhook',
			'data' => $body,
			'time_created' => time(),
		]);
		echo json(['message' => 'data received']);
		fastcgi_finish_request();


		# save data similar
		$simfoni_photo_ids = [];
		$object_similar_queue = [];
		foreach ($result as $key => $photo) {
			$simfoni_photo_id = $photo['meta_data']['simfoni_photo_id'];
			$simfoni_photo_url = $photo['url_original'];
			$simfoni_photo_ids[] = $simfoni_photo_id;

			//get simfoni_photo_id of result
			$object_similar = [];
			$list_photo_similar = $photo['similar_images'] ?? [];
			foreach ($list_photo_similar as $key2 => $similar_photo) {
				//prepare data to save
				$similar_photo_url = $similar_photo['url'];
				$similar_photo_percentage = floatval($similar_photo['similarity']) * 100;
				$similar_photo_cluster_id = $similar_photo['detail']['cluster_id'] ?? NULL;

				//skip same photo
				if ($similar_photo_url == $simfoni_photo_url) {
					continue;
				}


				//get similar photo id by photo url
				$photoDB = $this->db->get_where('photos', ['image_url' => $similar_photo_url])->row_array();
				if (!$photoDB) {
					continue;
				}

				$similar_simfoni_photo_id = $photoDB['simfoni_id'];

				//object
				$object_similar[] = [
					'simfoni_photo_id' => $simfoni_photo_id,
					'similarity' => $similar_photo_percentage,
					'similar_simfoni_photo_id' => $similar_simfoni_photo_id,
					'data' => json_encode($similar_photo),
					'cluster_id' => $similar_photo_cluster_id,
					'time_updated' => time(),
				];
			}

			//append to queue
			if (!empty($object_similar)) {
				$object_similar_queue[] = $object_similar;
			}
		}


		$this->db->trans_start();
		//save data
		# delete old data
		if (!empty($simfoni_photo_ids)) {
			$this->db->where_in('simfoni_photo_id', $simfoni_photo_ids);
			$this->db->delete('photos_similar');
		}

		# save new data
		foreach ($object_similar_queue as $key => $object) {
			$this->db->insert_batch('photos_similar', $object);
		}
		$complete = $this->db->trans_complete();

		# update progress
		//get total data retrieve
		$total_data_response = $this->db->get_where('logs', ['request_id'=>$request_id, 'type'=>'similarity_response'])->num_rows();
		//set percentage progress
		$percentage = floor(($total_data_response / $total_data_split)*100);
		$this->db->where('id', $request_id.'.progress');
		$this->db->update('ci_sessions', ['data' => $percentage, 'timestamp' => time()]);

		if ($complete) {
			echo json(['message' => 'success']);
			die;
		}

		http_response_code(500);
		echo json(['message' => 'Internal Server Error [422]']);
	}

	public function status()
	{
		$progress_url = '';
		$progress_percentage = 0;
		$progress_status = false;
		// ------------------------------------------------------------------------
		$this->db->limit(1);
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'similarity'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 30; // allow after 30 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = $progress['id'] ?? '';
				$progressID = remove_suffix($progressID, '.progress');

				$progress_url = site_url('similarity/progress').'?id='.$progressID;
				$progress_status = true;
			}
		}


		echo json([
			'message' => 'success',
			'data' => [
				'status' => $progress_status,
				'progress_url' => $progress_url,
				'percentage' => $progress_percentage,
			]
		]);
	}

}

/* End of file Similarity.php */
/* Location: ./application/modules/similarity/controllers/Similarity.php */