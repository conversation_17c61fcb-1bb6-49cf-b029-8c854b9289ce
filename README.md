# FPSI Simfoni Similarity Photo Checker

A PHP web application built with CodeIgniter 3 that synchronizes photography data from the FPSI (Federasi Photografi Seluruh Indonesia) Simfoni system and performs similarity analysis on photography submissions to detect potential duplicate or similar images.

## Project Overview

This application serves as a bridge between the FPSI Simfoni photography competition system and an AI-powered image similarity detection service. It helps photography competition organizers identify potentially similar or duplicate photo submissions across different competitions and photographers.

### Key Functionality

1. **Data Synchronization**: Automatically syncs member data and photography submissions from the FPSI Simfoni API
2. **Similarity Detection**: Integrates with an external AI service to analyze photo similarities
3. **Progress Tracking**: Real-time progress monitoring for synchronization and analysis processes
4. **Web Dashboard**: User-friendly interface for managing members, photos, and similarity results

## Architecture

### Core Modules

- **Simfoni Module** (`application/modules/simfoni/`): Handles synchronization with FPSI Simfoni API
- **Similarity Module** (`application/modules/similarity/`): Manages photo similarity analysis
- **User Module** (`application/modules/user/`): User dashboard and data management
- **Auth Module** (`application/modules/auth/`): Authentication and authorization
- **Webhook Module** (`application/modules/webhook/`): Database migration and webhook handling

### Database Schema

- **members**: FPSI photographer member information
- **photos**: Photography submission data from competitions
- **photos_similar**: Similarity analysis results and clustering data
- **logs**: System operation logs and audit trail
- **ci_sessions**: Session management and progress tracking

## Technical Features

### CodeIgniter 3 Enhancements
- **Security Improvement**: Document root moved to `public/` directory
- **Dynamic Base URL**: Environment-based configuration
- **HMVC Architecture**: Modular Extensions for better code organization
- **Database Migrations**: Automated schema management
- **Environment Variables**: Configuration via `.env` files
- **Background Processing**: Asynchronous task execution
- **Real-time Progress**: Server-Sent Events (SSE) for live updates

### External Integrations
- **FPSI Simfoni API**: Member and photo data synchronization
- **AI Similarity Service**: Image similarity analysis and clustering
- **Google Translate**: Multi-language support with auto-translation

## Requirements

### System Requirements
- **PHP**: 7.3 - 8.0.x
- **Composer**: For dependency management
- **MySQL/MariaDB**: Database server
- **Web Server**: Apache/Nginx with PHP-FPM support

### PHP Extensions
- `curl`: For API communications
- `json`: JSON data processing
- `mysqli`: Database connectivity
- `mbstring`: String manipulation
- `openssl`: Secure communications

### External Services
- FPSI Simfoni API access (requires API key)
- Image Similarity Analysis Service (requires API key)

## Installation & Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd fpsi-simfoni-similarity-photo-checker
```

### 2. Install Dependencies
```bash
composer install
```

### 3. Environment Configuration
```bash
cp .env.example .env
# Edit .env with your configuration
```

Required environment variables:
```env
# Database Configuration
DB_HOST=localhost
DB_NAME=your_database
DB_USER=your_username
DB_PASS=your_password

# FPSI Simfoni API
API_SIMFONI_URL=https://api.simfoni.fpsi.or.id/
API_SIMFONI_KEY=your_simfoni_api_key

# Similarity Analysis Service
API_SIMILARITY_URL=https://your-similarity-service.com/
API_SIMILARITY_KEY=your_similarity_api_key

# Application Environment
CI_ENV=development
```

### 4. Database Setup
```bash
# Run migrations
php index.php migrate
```

### 5. File Permissions
```bash
# Ensure proper permissions for storage
sudo chown -R www-data:www-data public/storage
sudo chmod -R 755 public/storage
```

## Docker Deployment

### Using Docker Compose
```bash
# Copy and configure docker-compose
cp docker-compose.example.yml docker-compose.yml

# Build and run
docker-compose up -d
```

The application will be available at `http://localhost:8080`

## API Endpoints

### Synchronization Endpoints
- `GET /simfoni/sync/chain` - Start complete synchronization process
- `GET /simfoni/sync/status` - Check synchronization status
- `GET /simfoni/sync/member` - Sync member data only
- `GET /simfoni/sync/photo` - Sync photo data only
- `GET /simfoni/sync/progress?id={request_id}` - Real-time progress tracking

### Similarity Analysis
- `GET /similarity` - Start similarity analysis
- `POST /similarity` - Retrieve similarity results
- `GET /similarity/status` - Check analysis status
- `GET /similarity/progress?id={request_id}` - Real-time progress tracking

### User Dashboard
- `GET /user/dashboard` - Main dashboard
- `GET /user/members` - Member management
- `GET /user/photos` - Photo management

### AJAX API (Authenticated)
- `GET /ajax/members/datatable` - Member data for DataTables
- `GET /ajax/photos/datatable` - Photo data for DataTables
- `GET /ajax/photos/{simfoni_photo_id}` - Get specific photo details

## Usage Workflow

### 1. Initial Setup
1. Configure environment variables
2. Run database migrations
3. Create user account through registration

### 2. Data Synchronization
1. Access `/simfoni/sync/chain` to start full synchronization
2. Monitor progress via the provided progress URL
3. Check `/simfoni/sync/status` for current sync status

### 3. Similarity Analysis
1. Ensure photos are synchronized first
2. Access `/similarity` to start analysis
3. Monitor progress and wait for completion
4. View results in the dashboard

### 4. Dashboard Management
1. Login to access user dashboard
2. Browse synchronized members and photos
3. Review similarity analysis results
4. Export or manage data as needed

## Development Guidelines

### Controller Standards
- All controllers must extend `MY_Controller`
- Use HMVC structure for modular development
- Implement proper error handling and logging

### Database Operations
- Use CodeIgniter's Query Builder for database operations
- Implement transactions for data consistency
- Use migrations for schema changes

### API Integration
- Implement retry logic for external API calls
- Use proper timeout settings
- Log all API interactions for debugging

### Background Processing
- Use `finish_request()` for background tasks
- Implement progress tracking for long-running operations
- Use proper session management for progress storage

## Troubleshooting

### Common Issues

1. **Sync Process Stuck**
   - Check if previous sync is still running
   - Wait for timeout (15 minutes) or manually clear sessions
   - Verify API credentials and connectivity

2. **Permission Errors**
   - Ensure `public/storage` has correct ownership
   - Check web server user permissions
   - Verify file upload limits in PHP configuration

3. **API Connection Issues**
   - Verify API URLs and keys in environment
   - Check network connectivity and firewall rules
   - Review API rate limits and quotas

### Logging
- Application logs: `application/logs/`
- Database logs: `logs` table
- Web server logs: Check Apache/Nginx error logs

## Security Considerations

- API keys stored in environment variables
- Session-based authentication
- CSRF protection enabled
- Input validation and sanitization
- SQL injection prevention via Query Builder
- File upload restrictions and validation

## Contributing

1. Follow CodeIgniter 3 coding standards
2. Use HMVC modular structure
3. Implement proper error handling
4. Add appropriate logging
5. Write clear documentation
6. Test thoroughly before deployment

## License

This project is proprietary software developed for FPSI (Federasi Photografi Seluruh Indonesia) photography competition management.
