<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_table_log_sync extends CI_Migration {

	protected $table = 'logs';

	public function __construct()
	{
		$this->load->dbforge();
		$this->load->database();
	}

	public function up()
	{
		$fields = [
			'id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => TRUE,
				'auto_increment' => TRUE,
			],
			'request_id' => [
				'type' => 'VARCHAR',
				'constraint' => 36,
				'null' => TRUE,
				'default' => NULL,
				'comment' => 'uuid v4 or random'
			],
			'type' => [
				'type' => 'VARCHAR',
				'constraint' => 30,
				'null' => FALSE,
			],
			'message' => [
				'type' => 'TEXT',
				'null' => FALSE,
			],
			'data' => [
				'type' => 'LONGTEXT',
				'null' => TRUE,
				'default' => NULL,
				'comment' => 'additional info (json)'
			],
			'time_created' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => TRUE,
				'null' => FALSE,
				'default' => 0,
			],
		];
		$this->dbforge->add_field($fields);
		$this->dbforge->add_key('id', TRUE);
		$this->dbforge->create_table($this->table, true);
	}

	public function down()
	{
		$this->dbforge->drop_table($this->table, TRUE);
	}

}

/* End of file 006_create_table_log_sync.php */
/* Location: ./application/migrations/006_create_table_log_sync.php */