FROM alpine:3.16

# Install Requirements
######################################################################
# minimal to run
RUN apk add \
	curl \
	nginx \
	\
	php \
	php-fpm \
	php-opcache

# Install Composer
RUN apk add \
	php-phar \
	php-iconv \
	php-openssl
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/bin --filename=composer

# composer package phpdotenv requirements
RUN apk add php-mbstring

# database / migration
RUN apk add php-mysqli

# session
RUN apk add php-session

# additional
RUN apk add php-curl



# NGINX Configurations
######################################################################
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx.app.conf /etc/nginx/http.d/default.conf



# PHP Configurations
######################################################################
# Create symlink so programs depending on `php` still function
#RUN ln -s /usr/bin/php8 /usr/bin/php || true
RUN ln -s /usr/sbin/php-fpm8 /usr/sbin/php-fpm || true

# Configure PHP-FPM
COPY docker/fpm-pool.conf /etc/php8/php-fpm.d/www.conf
COPY docker/php.ini /etc/php8/conf.d/custom.ini



# Application
######################################################################
# Set default path
WORKDIR /app

# Add application
COPY . .

# Install Composer Dependency
#COPY composer.json composer.lock ./
RUN composer install --working-dir=. --prefer-dist --no-scripts --no-dev && rm -rf /root/.composer

# Fix Permission (support volume)
RUN adduser -S www-data -u 1000
RUN sed -i "s/user = nobody/user = www-data/g" /etc/php8/php-fpm.d/www.conf
RUN sed -i "s/group = nobody/group = www-data/g" /etc/php8/php-fpm.d/www.conf



# Run APP
######################################################################
# Run
#CMD php-fpm -D && nginx -g 'daemon off;'
COPY docker/start.sh /
CMD ["/bin/sh", "/start.sh"]

# Configure a healthcheck to validate that everything is up & running
HEALTHCHECK --timeout=10s CMD curl --silent --fail http://127.0.0.1/fpm-ping