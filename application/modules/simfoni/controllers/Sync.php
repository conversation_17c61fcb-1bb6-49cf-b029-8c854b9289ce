<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Sync extends MY_Controller {

	protected $simfoni_url;
	protected $simfoni_key;

	public function __construct()
	{
		parent::__construct();
		$this->simfoni_url = $_SERVER['API_SIMFONI_URL'] ?? '';
		$this->simfoni_key = $_SERVER['API_SIMFONI_KEY'] ?? '';

		session_write_close();
	}

	public function chain()
	{
		// wait until sync member request done
		// ------------------------------------------------------------------------
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_member'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = rtrim($progress['id'], '.progress');
				http_response_code(403);
				echo json([
					'message' => 'SIMFONI synchronization process is still running',
					'data' => [
						'progress_url' => site_url('simfoni/sync/progress').'?id='.$progressID,
					]
				]);
				die;
			}
		}

		// prevent duplicate request
		// ------------------------------------------------------------------------
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_photo'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = rtrim($progress['id'], '.progress');
				http_response_code(403);
				echo json([
					'message' => 'SIMFONI synchronization process is still running',
					'data' => [
						'progress_url' => site_url('simfoni/sync/progress').'?id='.$progressID,
					]
				]);
				die;
			}
		}


		//set response
		echo json([
			'message' => 'Sync started in background',
			'data' => [
				'progress_url' => site_url('simfoni/sync/status'),
			]
		]);
		finish_request();
		set_time_limit(0);
		// Panggil member() secara internal
		ob_start();
		$this->member();
		ob_end_clean();

		// Panggil photo() secara internal
		ob_start();
		$this->photo();
		ob_end_clean();
	}

	public function status()
	{
		$this->db->order_by('timestamp', 'desc');
		$progressMember = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_member'])->row_array();
		$progressMemberRun = false;
		$progressMemberURL = '';
		$progressMemberPercentage = 0;
		if ($progressMember) {
			$progressMemberPercentage = $progressMember['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progressMember['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progressMemberPercentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressMemberID = rtrim($progressMember['id'], '.progress');
				$progressMemberRun = true;
				$progressMemberURL = site_url('simfoni/sync/progress').'?id='.$progressMemberID;
			}

			# reset progress percentage when progress done
			if (!$progressMemberRun) {
				$progressMemberPercentage = 0;
			}
		}

		$this->db->order_by('timestamp', 'desc');
		$progressPhoto = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_photo'])->row_array();
		$progressPhotoRun = false;
		$progressPhotoURL = '';
		$progressPhotoPercentage = 0;
		if ($progressPhoto) {
			$progressPhotoPercentage = $progressPhoto['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progressPhoto['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progressPhotoPercentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressPhotoID = rtrim($progressPhoto['id'], '.progress');
				$progressPhotoRun = true;
				$progressPhotoURL = site_url('simfoni/sync/progress').'?id='.$progressPhotoID;
			}

			# reset progress percentage when progress done
			if (!$progressPhotoRun) {
				$progressPhotoPercentage = 0;
			}
		}


		echo json([
			'message' => 'success',
			'data' => [
				'member' => [
					'in_progress' => $progressMemberRun,
					'progress_url' => $progressMemberURL,
					'percentage' => intval($progressMemberPercentage),
				],
				'photo' => [
					'in_progress' => $progressPhotoRun,
					'progress_url' => $progressPhotoURL,
					'percentage' => intval($progressPhotoPercentage),
				],
			]
		]);
	}

	public function progress()
	{
		header('Content-Type: text/event-stream');

		$progressID = $this->input->get('id');

		//check progress exist
		$progress = $this->db->get_where('ci_sessions', ['id' => $progressID.'.progress'])->row_array();
		if (!$progress) {
			echo "data: ".json_encode(['message' => 'Progress Not Found', 'code' => 'error']) . "\n\n";
			connection_aborted();
			die;
		}


		while (true) {
			$progress_percentage = $this->db->get_where('ci_sessions', ['id' => $progressID.'.progress'])->row_array()['data'] ?? 0;

			//print progress
			echo "data: ".json_encode([
				'message' => 'ok',
				'data' => [
					'percentage' => $progress_percentage,
				]
			])."\n\n";;

			if ($progress_percentage >= 100) {
				connection_aborted();
				die;
			}

			// Flush buffer (force sending data to client)
			flush();

			// Wait 2 seconds for the next message / event
			sleep(2);
		}
	}

	public function member()
	{
		$list_member = $this->db->get('members')->result_array();
		$simfoni_ids = array_column($list_member, 'simfoni_id');
		$current_time = time();
		$request_id = generateUUIDv4();

		// wait until sync photo request done
		// ------------------------------------------------------------------------
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_photo'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = rtrim($progress['id'], '.progress');
				http_response_code(403);
				echo json([
					'message' => 'The photo synchronization process is still running',
					'data' => [
						'progress_url' => site_url('simfoni/sync/progress').'?id='.$progressID,
					]
				]);
				die;
			}
		}

		//prevent duplicate request
		// ------------------------------------------------------------------------
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_member'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = rtrim($progress['id'], '.progress');
				http_response_code(403);
				echo json([
					'message' => 'The last synchronization process is still running',
					'data' => [
						'progress_url' => site_url('simfoni/sync/progress').'?id='.$progressID,
					]
				]);
				die;
			}
		}


		// ------------------------------------------------------------------------
		//get last member
		$url = $this->simfoni_url."get-last-id";

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_TIMEOUT, 30);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			'key: '.$this->simfoni_key,
		]);
		$response = curl_exec($ch);
		$info = curl_getinfo($ch);
		curl_close($ch);


		$http_code = $info['http_code'] ?? '';
		if ($http_code !== 200) {
			http_response_code($http_code);
			echo json(['message' => 'Get last member failed ['.$http_code.']']);
			die;
		}

		// ------------------------------------------------------------------------
		$data = json_decode($response, true);
		$lastId = $data['simfoni']['results']['last_id'] ?? '';
		
		// Ambil angka dari last_id (contoh: "FP00604" -> 604)
		$total_simfoni_member = intval(substr($lastId, 2));
		$total_member_sync = count($list_member);

		# book progress
		$this->db->insert('ci_sessions', [
			'id' => $request_id.'.progress',
			'ip_address' => 'sync_simfoni_member',
			'timestamp' => time(),
			'data' => 0,
		]);


		echo json([
			'message' => 'Sync '.$total_simfoni_member.' members ['.number_format($total_member_sync).' synchronized]',
			'data' => [
				'total' => $total_simfoni_member,
				'total_sync' => $total_member_sync,
				'progress_url' => site_url('simfoni/sync/progress').'?id='.$request_id,
			]
		]);
		finish_request();
		print_log('['.$request_id.'] Sync SIMFONI Member :: START');
		# add log
		$this->db->insert('logs', [
			'request_id' => $request_id,
			'type' => 'member',
			'message' => 'Sync Member START',
			'data' => json_encode(['request_id'=>$request_id]),
			'time_created' => time(),
		]);


		$object_insert = [];
		$object_update = [];
		$object_failed = [];
		$total_data = 0;
		for ($i=1; $i <= $total_simfoni_member; $i++) { 
			// Format ulang ke kode seperti "FP00001"
			$fpnumber = 'FP' . str_pad($i, 5, '0', STR_PAD_LEFT);
			

			$total_retry_sync = 0;
			$total_retry_sync_max = 5;
			STAGE_SYNC_MEMBER:
			//get member info
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $this->simfoni_url.'member?id='.$fpnumber);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 30);
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
				'key: '.$this->simfoni_key,
			]);
			$response = curl_exec($ch);
			$info = curl_getinfo($ch);
			curl_close($ch);

			$status_code = $info['http_code'] ?? '';
			$status = ($status_code == 200) ? 'SUCCESS' : 'FAILED';
			$message_add = '';
			if ($status_code == 400) {
				$message_add = ' :: '.$response;
			}
			print_log('['.$request_id.'] Sync Member '.$fpnumber.' -> '.$status.' ['.$status_code.']'.$message_add);
			if ($status_code != 200) {
				# retry sync
				if ($total_retry_sync < $total_retry_sync_max) {
					$total_retry_sync++;
					print_log('['.$request_id.'] Sync Member '.$fpnumber.' -> RETRY '.$total_retry_sync);
					usleep(100000); // wait 100ms
					goto STAGE_SYNC_MEMBER;
				}

				$object_failed[] = $fpnumber;

				# add log
				$this->db->insert('logs', [
					'request_id' => $request_id,
					'type' => 'member',
					'message' => 'Sync Member `'.$fpnumber.'` FAILED ['.$status_code.']',
					'data' => $response,
					'time_created' => time(),
				]);

				# update progress
				$percentage = intval(($i / $total_simfoni_member)*100);
				$this->db->where('id', $request_id.'.progress');
				$this->db->update('ci_sessions', ['data' => $percentage, 'timestamp' => time()]);
				continue;
			}

			// ------------------------------------------------------------------------
			$data = json_decode($response, true);
			$result = $data['simfoni']['results'] ?? [];

			$simfoni_id = $result['id'];
			if (!in_array($simfoni_id, $simfoni_ids)) {
				$object_insert[] = [
					'simfoni_id' => $simfoni_id,
					'name' => $result['nama_lengkap'],
					'honors' => $result['gelar_fotografi'],
					'time_created' => $current_time,
					'time_updated' => $current_time,
				];
			} else {
				$object_update[] = [
					'simfoni_id' => $simfoni_id,
					'name' => $result['nama_lengkap'],
					'honors' => $result['gelar_fotografi'],
					'time_updated' => $current_time,
				];
			}
			$total_data++;

			# update progress
			$percentage = intval(($i / $total_simfoni_member)*100);
			$this->db->where('id', $request_id.'.progress');
			$this->db->update('ci_sessions', ['data' => $percentage, 'timestamp' => time()]);
		}


		# prepare sync data
		// ------------------------------------------------------------------------
		$this->db->trans_start();
		# insert new data
		if (!empty($object_insert)) {
			$this->db->insert_batch('members', $object_insert);
		}
		# update data
		foreach ($object_update as $object) {
			$this->db->where('simfoni_id', $object['simfoni_id']);
			$this->db->update('members', $object);
		}


		//prepare message log
		$total_insert = count($object_insert);
		$total_update = count($object_update);
		$total_fail = count($object_failed);
		$complete = $this->db->trans_complete();

		# add log
		$this->db->insert('logs', [
			'request_id' => $request_id,
			'type' => 'member',
			'message' => 'Sync Member FINISH',
			'data' => json_encode([
				'request_id' => $request_id,
				'data' => [
					'added' => $total_insert,
					'updated' => $total_update,
					'failed' => $total_fail,
					'list_fail' => $object_failed,
				]
			]),
			'time_created' => time(),
		]);
		if ($complete) {
			$message = '['.$total_data.'/'.$total_simfoni_member.'] '. $total_insert.' added, '.$total_update.' updated, '.$total_fail.' failed ['.json_encode($object_failed).']';
			echo json(['message' => $message]);
			print_log('['.$request_id.'] Sync SIMFONI Member :: FINISH :: '.$message);
			return;
		}

		http_response_code(500);
		echo json(['message' => 'Internal Server Error [422]']);
		print_log('['.$request_id.'] Sync SIMFONI Member :: FINISH :: FAILED save to DB');
	}

	public function photo()
	{
		$list_member = $this->db->get('members')->result_array();
		$total_photo = $this->db->query('SELECT COUNT(id) AS total_photo FROM `photos`')->row_array()['total_photo'] ?? 0;
		$request_id = generateUUIDv4();

		// prevent sync when member not synchronized
		// ------------------------------------------------------------------------
		if (empty($list_member)) {
			http_response_code(403);
			echo json(['message' => 'The member data has not been synchronized']);
			die;
		}

		// wait until sync member request done
		// ------------------------------------------------------------------------
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_member'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = rtrim($progress['id'], '.progress');
				http_response_code(403);
				echo json([
					'message' => 'The member synchronization process is still running',
					'data' => [
						'progress_url' => site_url('simfoni/sync/progress').'?id='.$progressID,
					]
				]);
				die;
			}
		}

		// prevent duplicate request
		// ------------------------------------------------------------------------
		$this->db->order_by('timestamp', 'desc');
		$progress = $this->db->get_where('ci_sessions', ['ip_address' => 'sync_simfoni_photo'])->row_array();
		if ($progress) {
			$progress_percentage = $progress['data'] ?? 0;


			$resync_time = false;
			$resync_time_allow = 60 * 15; // allow after 15 minutes
			if (((time() - $progress['timestamp']) >= $resync_time_allow)) {
				$resync_time = true;
			}

			$resync_percentage = false;
			if ($progress_percentage >= 100) {
				$resync_percentage = true;
			}

			if ($resync_time == false && $resync_percentage == false) {
				$progressID = rtrim($progress['id'], '.progress');
				http_response_code(403);
				echo json([
					'message' => 'The last synchronization process is still running',
					'data' => [
						'progress_url' => site_url('simfoni/sync/progress').'?id='.$progressID,
					]
				]);
				die;
			}
		}


		// ------------------------------------------------------------------------
		# book progress
		$this->db->insert('ci_sessions', [
			'id' => $request_id.'.progress',
			'ip_address' => 'sync_simfoni_photo',
			'timestamp' => time(),
			'data' => 0,
		]);


		echo json([
			'message' => "Sync ".number_format(count($list_member))." member's photos [".number_format($total_photo)." synchronized]",
			'data' => [
				'total' => count($list_member),
				'progress_url' => site_url('simfoni/sync/progress').'?id='.$request_id,
			]
		]);
		finish_request();
		print_log('['.$request_id.'] Sync SIMFONI Photo :: START ['.number_format($total_photo).' synchronized]');
		# add log
		$this->db->insert('logs', [
			'request_id' => $request_id,
			'type' => 'photo',
			'message' => 'Sync Photo START ['.number_format($total_photo).' synchronized]',
			'data' => json_encode(['request_id'=>$request_id]),
			'time_created' => time(),
		]);


		$object_failed = [];
		$total_data = 0;
		$total_result = 0;
		$object_photos2 = [];
		foreach ($list_member as $i => $member) {
			$fpnumber = $member['simfoni_id'];


			$total_retry_sync = 0;
			$total_retry_sync_max = 5;
			STAGE_SYNC_PHOTO:
			//get photo accepted
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $this->simfoni_url.'accepted?id='.$fpnumber);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_TIMEOUT, 30);
			curl_setopt($ch, CURLOPT_HTTPHEADER, [
				'key: '.$this->simfoni_key,
			]);
			$response = curl_exec($ch);
			$info = curl_getinfo($ch);
			curl_close($ch);
			// ------------------------------------------------------------------------
			$data = json_decode($response, true);
			$result = $data['simfoni']['results'] ?? [];

			$status_code = $info['http_code'] ?? '';
			$status = ($status_code == 200) ? 'SUCCESS' : 'FAILED';
			$message_add = '';
			switch ($status_code) {
				case '200':
					$total_result += count($result);
					$message_add = ' :: '.number_format(count($result)).' photos';
					break;
				case '400':
					$message_add = ' :: '.$response;
					break;
				default:
					// code...
					break;
			}
			print_log('['.$request_id.'] Sync Photo '.$fpnumber.' -> '.$status.' ['.$status_code.']'.$message_add);
			if ($status_code != 200) {
				# retry sync
				if ($total_retry_sync < $total_retry_sync_max) {
					$total_retry_sync++;
					print_log('['.$request_id.'] Sync Photo '.$fpnumber.' -> RETRY '.$total_retry_sync);
					usleep(100000); // wait 100ms
					goto STAGE_SYNC_PHOTO;
				}

				$object_failed[] = $fpnumber;

				# add log
				$this->db->insert('logs', [
					'request_id' => $request_id,
					'type' => 'photo',
					'message' => 'Sync Photo `'.$fpnumber.'` FAILED ['.$status_code.']',
					'data' => $response,
					'time_created' => time(),
				]);

				# update progress
				$percentage = intval(($i+1) / count($list_member)*100);
				$this->db->where('id', $request_id.'.progress');
				$this->db->update('ci_sessions', ['data' => $percentage, 'timestamp' => time()]);
				continue;
			}

			// ------------------------------------------------------------------------
			$data = json_decode($response, true);
			$result = $data['simfoni']['results'] ?? [];

			//prepare photo
			$object_photos = [];
			foreach ($result as $simfoni_photo) {
				$simfoni_photo_id = $simfoni_photo['foto_id'];
				$simfoni_photo_title = $simfoni_photo['judul_foto'] ?? '';
				$simfoni_photo_folder = $simfoni_photo['folder'] ?? '';
				$simfoni_photo_filename = $simfoni_photo['filename'] ?? '';
				$simfoni_photo_url = 'https://simfoni.fpsi.or.id/data/accepted_photo/'.$simfoni_photo_folder.'/'.rawurlencode($simfoni_photo_filename);
				$simfoni_event_short = $simfoni_photo['event_shortname'] ?? '';
				$simfoni_event_name = $simfoni_photo['event_name'] ?? '';
				$simfoni_event_year = $simfoni_photo['tahun'] ?? '';
				$current_time = time();

				//append data photo
				$object_photos[] = [
					'simfoni_id' => $simfoni_photo_id,
					'simfoni_member_id' => $fpnumber,
					'title' => $simfoni_photo_title,
					'event_shortname' => $simfoni_event_short,
					'event_name' => $simfoni_event_name,
					'year' => $simfoni_event_year,
					'category_name' => $simfoni_photo['kategori'],
					'photo_no' => $simfoni_photo['no_foto'],
					'image_url' => $simfoni_photo_url,
					'time_updated' => $current_time,
				];
			}


			// append data
			$object_photos2[$fpnumber] = $object_photos;

			$total_data++;

			# update progress
			$percentage = intval(($i+1) / count($list_member)*100);
			$this->db->where('id', $request_id.'.progress');
			$this->db->update('ci_sessions', ['data' => $percentage, 'timestamp' => time()]);
		}


		# prepare sync data
		// ------------------------------------------------------------------------
		$this->db->trans_start();
		foreach ($object_photos2 as $fpnumber => $object_photos) {
			// remove old data
			$this->db->delete('photos', ['simfoni_member_id' => $fpnumber]);

			// insert new data
			if (!empty($object_photos)) {
				$this->db->insert_batch('photos', $object_photos);
			}
		}


		//prepare message log
		$total_member = count($list_member);
		$total_fail = count($object_failed);
		$complete = $this->db->trans_complete();

		# add log
		$this->db->insert('logs', [
			'request_id' => $request_id,
			'type' => 'photo',
			'message' => 'Sync Photo FINISH [Result: '.number_format($total_result).']',
			'data' => json_encode([
				'request_id' => $request_id,
				'data' => [
					'failed' => $total_fail,
					'list_fail' => $object_failed,
				]
			]),
			'time_created' => time(),
		]);
		if ($complete) {
			$message = '['.$total_data.'/'.count($list_member).'] '.count($object_failed).' failed ['.json_encode($object_failed).'] -> Total '.number_format($total_result).' photos';
			print_log('['.$request_id.'] Sync SIMFONI Photo :: FINISH :: '.$message);
			return;
		}

		http_response_code(500);
		echo json(['message' => 'Internal Server Error [422]']);
		print_log('['.$request_id.'] Sync SIMFONI Photo :: FINISH :: FAILED save to DB');
	}

}

/* End of file Sync.php */
/* Location: ./application/modules/simfoni/controllers/Sync.php */