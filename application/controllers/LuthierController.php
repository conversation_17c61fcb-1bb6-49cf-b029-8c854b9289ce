<?php

/**
 * <PERSON><PERSON><PERSON> default controller - DO NOT EDIT OR REMOVE THIS FILE!
 *
 * Because <PERSON><PERSON><PERSON><PERSON> checks the existence of the file/class/method of the controller
 * pointed in all our routes, this allows to create dynamic routes without hacking/extending
 * the CI_Router class.
 */

defined('BASEPATH') OR exit('No direct script access allowed');

class LuthierController extends MY_Controller
{
    /**
     * Luthier CI fake index method
     */
    public function index()
    {
    }
}
