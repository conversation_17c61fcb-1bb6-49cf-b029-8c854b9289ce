<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Members extends MY_Controller {

	public function index()
	{
		$data['url_ajax'] = site_url('ajax/members');
		$data['page_title'] = lang('Member List');
		view('members', $data);
	}

	public function ajax_datatable()
	{
		$this->datatables->select('m.id, m.simfoni_id, m.name, m.honors, m.time_updated');
		$this->datatables->from('members m');
		echo $this->datatables->generate();
	}

}

/* End of file Members.php */
/* Location: ./application/modules/user/controllers/Members.php */