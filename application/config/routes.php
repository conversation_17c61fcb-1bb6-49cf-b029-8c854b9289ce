<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'welcome';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['auth/login']['GET'] = 'auth/Login/index';
$route['auth/login/process_ajax']['POST'] = 'auth/Login/process_ajax';
$route['auth/register']['GET'] = 'auth/Register/index';
$route['auth/register/process_ajax']['POST'] = 'auth/Register/process_ajax';
$route['auth/forgot']['GET'] = 'auth/Forgot/index';
$route['auth/forgot/forgot_process_ajax']['POST'] = 'auth/Forgot/forgot_process_ajax';
$route['auth/forgot/recovery/(:any)/(:any)']['GET'] = 'auth/Forgot/recovery/$1/$2';
$route['auth/forgot/recovery_process_ajax']['POST'] = 'auth/Forgot/recovery_process_ajax';

$route['auth/activation/resend']['GET'] = 'auth/Activation/resend';
$route['auth/activation/resend_process_ajax']['POST'] = 'auth/Activation/resend_process_ajax';
$route['auth/activation/activate/(:any)/(:any)']['GET'] = 'auth/Activation/activate/$1/$2';
$route['auth/activation/activate_process_ajax']['POST'] = 'auth/Activation/activate_process_ajax';

$route['auth/logout']['GET'] = 'auth/Logout/index';

$route['user/dashboard']['GET'] = 'user/Dashboard/index';
$route['user/settings']['GET'] = 'user/Settings/index';
$route['user/settings/ajax_change_email']['PATCH'] = 'user/Settings/ajax_change_email';
$route['user/settings/ajax_change_password']['PATCH'] = 'user/Settings/ajax_change_password';

$route['webhook/migrate']['GET'] = 'webhook/Migrate/index';

# disable auto route
if ((php_sapi_name() !== 'cli')) {
	$route['(.*)'] = 'welcome/404';
}



$route = Luthier\Route::getRoutes();