<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_table_members extends CI_Migration {

	protected $table = 'members';

	public function __construct()
	{
		$this->load->dbforge();
		$this->load->database();
	}

	public function up()
	{
		$fields = [
			'id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => TRUE,
				'auto_increment' => TRUE,
				'comment' => 'member_id'
			],
			'simfoni_id' => [
				'type' => 'VARCHAR',
				'constraint' => 20,
				'unique' => TRUE,
				'null' => FALSE,
				'comment' => 'simfoni fpnumber'
			],
			'name' => [
				'type' => 'VARCHAR',
				'constraint' => 255,
				'null' => FALSE
			],
			'honors' => [
				'type' => 'VARCHAR',
				'constraint' => 255,
				'null' => TRUE,
				'default' => NULL,
			],
			'time_created' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => TRUE,
				'null' => FALSE,
				'default' => 0,
			],
			'time_updated' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => TRUE,
				'null' => FALSE,
				'default' => 0,
			],
		];
		$this->dbforge->add_field($fields);
		$this->dbforge->add_key('id', TRUE);
		$this->dbforge->create_table($this->table, true);
	}

	public function down()
	{
		$this->dbforge->drop_table($this->table, TRUE);
	}

}

/* End of file 004_create_table_members.php */
/* Location: ./application/migrations/004_create_table_members.php */