<?php
$theme = 'https://adminlte.io/themes/v3/';
$app_logo = base_url('logo-fpsi.png');
$app_name = 'SIMFONI Photo Similarity Checker';
$app_url = site_url();
$app_lang_config = $this->config->item('language_cookie_name');
$app_lang = $this->input->cookie($app_lang_config) ?? 'us';
if ($app_lang == 'en') {
	$app_lang = 'us';
}
?>
<!DOCTYPE html>
<!--
<?=$app_name . "\n" ?>
powered by: UNIQDEV
dev: kangsunu
-->
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>@yield('page_title')</title>

<!-- Google Font: Source Sans Pro -->
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
<!-- Font Awesome Icons -->
<link rel="stylesheet" href="<?=$theme?>plugins/fontawesome-free/css/all.min.css">
<!-- SweetAlert2 -->
<link rel="stylesheet" href="<?=$theme?>plugins/sweetalert2/sweetalert2.min.css">
<!-- Datatable -->
<link rel="stylesheet" href="https://cdn.datatables.net/2.3.2/css/dataTables.bootstrap4.min.css" />
<!-- Theme style -->
<link rel="stylesheet" href="<?=$theme?>dist/css/adminlte.min.css">
<!-- Override style -->
<style>
.wrapper > .content-wrapper { padding-bottom: 1px; }

/* fix datatable table-responsive */
.table-responsive > .dt-container .row.justify-content-between {
	margin-bottom: .5rem !important;
}
</style>
<?="\n" ?>
@yield('css')

</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

	<!-- Navbar -->
	<nav class="main-header navbar navbar-expand navbar-white navbar-light">
		<!-- Left navbar links -->
		<ul class="navbar-nav">
			<li class="nav-item">
				<a class="nav-link" data-widget="pushmenu" href="javascript:void(0)" role="button"><i class="fas fa-bars"></i></a>
			</li>
		</ul>

		<!-- Right navbar links -->
		<ul class="navbar-nav ml-auto">
			<!-- User Dropdown Menu -->
			<li class="nav-item dropdown">
				<a class="nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown">
					<i class="fas fa-user"></i>
				</a>
				<ul class="dropdown-menu dropdown-menu-right border-0 shadow">
					<li><a class="dropdown-item" href="<?=site_url() ?>">Home</a></li>
					<li><a class="dropdown-item" href="<?=site_url('user/settings') ?>">Settings</a></li>
					<li class="dropdown-divider"></li>
					<li><a class="dropdown-item" href="<?=site_url('auth/logout') ?>"><i class="fas fa-sign-out-alt mr-2"></i> Logout</a></li>
				</ul>
			</li>
		</ul>
	</nav>
	<!-- /.navbar -->

	<!-- Main Sidebar Container -->
	<aside class="main-sidebar sidebar-dark-primary">
		<!-- Brand Logo -->
		<a href="<?=$app_url ?>" class="brand-link" title="<?=htmlentities($app_name) ?>">
			<img src="<?=$app_logo?>" alt="<?=$app_name ?> Logo" class="brand-image img-circle" style="opacity: .8">
			<span class="brand-text font-weight-light"><?=$app_name ?></span>
		</a>

		<!-- Sidebar -->
		<div class="sidebar">
			<!-- Sidebar Menu -->
			<nav class="mt-2">
				<ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
					<li class="nav-header">NAVIGATION MENU</li>
					<li class="nav-item">
						<a href="<?=site_url('user/dashboard') ?>" class="nav-link">
							<i class="nav-icon fas fa-home"></i>
							<p>Dashboard</p>
						</a>
					</li>
					<li class="nav-item">
						<a href="<?=site_url('user/members') ?>" class="nav-link">
							<i class="nav-icon fas fa-users"></i>
							<p>Member List</p>
						</a>
					</li>
					<li class="nav-item">
						<a href="<?=site_url('user/photos') ?>" class="nav-link">
							<i class="nav-icon fas fa-images"></i>
							<p>Photo List</p>
						</a>
					</li>
					<!--
					<li class="nav-item">
						<a href="javascript:void(0)" class="nav-link">
							<i class="nav-icon fas fa-images"></i>
							<p>Photo List  <i class="right fas fa-angle-left"></i></p>
						</a>
						<ul class="nav nav-treeview">
							<li class="nav-item">
								<a href="<?=site_url('user/photos') ?>" class="nav-link">
									<i class="far fa-circle nav-icon"></i>
									<p>Photo Table</p>
								</a>
							</li>
							<li class="nav-item">
								<a href="<?=site_url('user/photos-thumbnail') ?>" class="nav-link">
									<i class="far fa-circle nav-icon"></i>
									<p>Photo Thumbnail</p>
								</a>
							</li>
						</ul>
					</li>
					-->
					<li class="divider" style="border-bottom: 1px solid #4f5962;"></li>
					<li class="nav-item">
						<a href="<?=site_url('logout') ?>" class="nav-link">
							<i class="nav-icon fas fa-sign-out-alt"></i>
							<p>Logout</p>
						</a>
					</li>
				</ul>
			</nav>
			<!-- /.sidebar-menu -->
		</div>
		<!-- /.sidebar -->
	</aside>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		@yield('content')
	</div>
	<!-- /.content-wrapper -->

	<!-- Main Footer -->
	<footer class="main-footer text-sm">
		<div class="col-12">
			<!-- To the right -->
			<div class="float-right d-none d-sm-inline">
				Powered by <a href="https://uniq.id" target="_blank"><b>UNIQDEV</b></a>
			</div>
			<!-- Default to the left -->
			<strong><a href="<?=site_url() ?>">SIMFONI Photo Similarity Checker</a> &copy; 2025.</strong> All rights reserved.
		</div>
	</footer>
</div>
<!-- ./wrapper -->

<!-- REQUIRED SCRIPTS -->
<!-- jQuery -->
<script src="<?=$theme?>plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="<?=$theme?>plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- SweetAlert2 -->
<script src="<?=$theme?>plugins/sweetalert2/sweetalert2.min.js"></script>
<!-- Datatable -->
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.3.2/js/dataTables.bootstrap4.min.js"></script>
<!-- AdminLTE App -->
<!-- <script src="<?=$theme?>dist/js/adminlte.min.js"></script> -->
<script src="<?=base_url()?>tools/time.js"></script>
<script type="text/javascript">
//auto active sidebar by url prefix
$(function() {
	/** add active class and stay opened when selected */
	var url = window.location.origin + window.location.pathname;
	var urlText = url.toString();

	// for sidebar menu entirely but not cover treeview
	$('ul.nav-sidebar a').filter(function() {
		return this.href == url || urlText.startsWith(this.href + '/');
	}).addClass('active');

	// for treeview
	$('ul.nav-treeview a').filter(function() {
		return this.href == url || urlText.startsWith(this.href + '/');
	}).parentsUntil(".nav-sidebar > .nav-treeview").addClass('menu-open').prev('a').addClass('active');
});
</script>
<!-- Set Language -->
<script>
async function setCookie(cname, cvalue, extime) {
	const d = new Date();
	d.setTime(d.getTime() + (extime*1000));
	let expires = "expires="+ d.toUTCString();
	document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}
async function changeLanguage(lang){
	document.write('<?=lang('Loading') ?>...');
	var languageKey = '<?=$app_lang_config ?>';
	var expire = 7200;
	setCookie(languageKey, lang, expire);

	window.location = window.location.href;
}
</script>
<script>
//time format
$(function() {
	$('[data-time]').each(function() {
		let dataTime = $(this).data('time');
		let dataTimeFormat = $(this).data('time-format') ?? 'Y-M-d H:i:s';
		let dataTimeLocal = php_date(dataTimeFormat, dataTime);
		$(this).text(dataTimeLocal);
	});
});
</script>

@yield('js')

</body>
</html>