<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_table_photos extends CI_Migration {

	protected $table = 'photos';

	public function __construct()
	{
		$this->load->dbforge();
		$this->load->database();
	}

	public function up()
	{
		$fields = [
			'id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => TRUE,
				'auto_increment' => TRUE,
				'comment' => 'photo_id'
			],
			'simfoni_id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => TRUE,
				'unique' => TRUE,
				'null' => FALSE,
				'comment' => 'simfoni `foto_id`'
			],
			'simfoni_member_id' => [
				'type' => 'VARCHAR',
				'constraint' => 20,
				'null' => TRUE,
				'comment' => 'fpnumber'
			],
			'title' => [
				'type' => 'VARCHAR',
				'constraint' => 255,
				'null' => FALSE
			],
			'event_shortname' => [
				'type' => 'VARCHAR',
				'constraint' => 255,
				'null' => FALSE
			],
			'event_name' => [
				'type' => 'VARCHAR',
				'constraint' => 255,
				'null' => FALSE
			],
			'year' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => TRUE,
				'null' => FALSE
			],
			'category_name' => [
				'type' => 'VARCHAR',
				'constraint' => 255,
				'null' => FALSE
			],
			'photo_no' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => TRUE,
				'null' => FALSE
			],
			'image_url' => [
				'type' => 'TEXT',
				'null' => FALSE
			],
			'time_updated' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => TRUE,
				'null' => FALSE,
				'default' => 0,
			],
		];
		$this->dbforge->add_field($fields);
		$this->dbforge->add_key('id', TRUE);
		$this->dbforge->add_field('CONSTRAINT FOREIGN KEY (`simfoni_member_id`) 
			REFERENCES `members`(`simfoni_id`) ON DELETE CASCADE ON UPDATE CASCADE');
		$this->dbforge->create_table($this->table, true);
	}

	public function down()
	{
		$this->dbforge->drop_table($this->table, TRUE);
	}

}

/* End of file 005_create_table_photos.php */
/* Location: ./application/migrations/005_create_table_photos.php */