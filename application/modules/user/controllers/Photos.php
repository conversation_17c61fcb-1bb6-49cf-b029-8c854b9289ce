<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Photos extends MY_Controller {

	public function index()
	{
		$this->db->select('COUNT(DISTINCT ps.simfoni_photo_id) AS total_similar, MAX(ps.time_updated) AS time_updated');
		$photo_similar = $this->db->get('photos_similar ps')->row_array();

		$data['total_photo_similar'] = $photo_similar['total_similar'] ?? 0;
		$data['photo_similar_time'] = $photo_similar['time_updated'] ?? 0;
		$data['url_ajax'] = site_url('ajax/photos');
		$data['page_title'] = lang('Photo List');
		view('photos', $data);
	}

	public function ajax_datatable()
	{
		$filter_similarity = $this->input->get_post('filter_similarity');

		$this->datatables->select("p.simfoni_id, p.title, m.name AS member_name, 'checked' AS similarity, p.image_url, COUNT(ps.id) AS total_similar");
		$this->datatables->from('photos p');
		$this->datatables->join('members m', 'p.simfoni_member_id = m.simfoni_id', 'left');
		$this->datatables->join('photos_similar ps', 'p.simfoni_id = ps.simfoni_photo_id', 'left');
		if (!empty($filter_similarity)) {
			$this->datatables->where('ps.similarity >=', $filter_similarity);
		}
		$this->datatables->group_by('p.simfoni_id');
		echo $this->datatables->generate();
	}

	public function get($simfoni_photo_id)
	{
		$this->db->select('p.*, m.name AS member_name, m.honors AS member_honors');
		$this->db->from('photos p');
		$this->db->join('members m', 'p.simfoni_member_id = m.simfoni_id', 'left');
		$this->db->where('p.simfoni_id', $simfoni_photo_id);
		$photo = $this->db->get()->row_array();
		if (!$photo) {
			http_response_code(404);
			echo json(['message' => translate('Data Not Found')]);
			die;
		}

		$this->db->select('
			ps.*,
			m.simfoni_id AS simfoni_member_id, m.name AS member_name, m.honors AS member_honors,
			pps.year AS photo_year,
			pps.image_url
		');
		$this->db->from('photos_similar ps');
		$this->db->join('photos pps', 'ps.similar_simfoni_photo_id = pps.simfoni_id', 'left');
		$this->db->join('members m', 'pps.simfoni_member_id = m.simfoni_id', 'left');
		$this->db->where('ps.simfoni_photo_id', $photo['simfoni_id']);
		$this->db->order_by('ps.similarity', 'desc');
		$list_similar_images = $this->db->get()->result_array();


		$photo['list_similar'] = $list_similar_images;

		echo json([
			'message' => 'success',
			'data' => $photo
		]);
	}

}

/* End of file Photos.php */
/* Location: ./application/modules/user/controllers/Photos.php */