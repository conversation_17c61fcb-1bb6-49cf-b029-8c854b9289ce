<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class AuthMustLoggedinAPI implements <PERSON><PERSON>er\MiddlewareInterface
{
	public function run($args)
	{
		$CI =& get_instance();

		if (!$CI->session->userdata('logged_in')) {
			http_response_code(401);
			echo json(['message' => "You're not logged in"]);
			die;
		}
	}
}

/* End of file AuthMustLoggedinAPI.php */
/* Location: ./application/middleware/AuthMustLoggedinAPI.php */