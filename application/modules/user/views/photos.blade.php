@extends('layouts.app')
@section('page_title', $page_title)
@section('css')
<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
<style>
.progress-text {
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none; /* biar nggak ganggu interaksi */
}
</style>
@endsection
@section('content')
<!-- Content Header (Page header) -->
<section class="content-header">
	<div class="container-fluid">
		<div class="row mb-2">
			<div class="col-sm-12">
				<h1 class="m-0"><?=lang('Photo List') ?></h1>
			</div><!-- /.col -->
		</div><!-- /.row -->
	</div><!-- /.container-fluid -->
</section>
<!-- /.content-header -->

<!-- Main content -->
<section class="content" id="section-page">
	<div class="container-fluid">
		<div class="small-box bg-success mb-2">
			<div class="inner">
				<h3><?=number_format($total_photo_similar) ?></h3>
				<p>Total Similar Photo</p>
			</div>
			<div class="icon">
				<i class="ion ion-images"></i>
			</div>
			<a href="javascript:checkSimilarity(0)" class="small-box-footer text-left pl-2">Last Check: <span data-time="<?=$photo_similar_time ?>"></span> <span class="float-right mr-2">[ Check Similarity ]</span></a>
		</div>
		<div id="progress" class="progress position-relative mb-2 d-none" style="height: 35px;">
			<div id="progress-bar" class="progress-bar bg-primary progress-bar-striped progress-bar-animated" style="width: 0%;"></div>
			<span id="progress-text" class="progress-text position-absolute w-100 text-center">Not Running</span>
		</div>

		<button id="check-similarity" type="button" class="btn bg-indigo btn-block d-none mb-2" onclick="checkSimilarity()">Check Similarity</button>

		<!-- Default box -->
		<div class="card">
			<div class="card-header">
				<div class="row">
					<div class="col-4">
						<input type="number" min="50" max="100" id="filter-similarity" class="form-control form-control-sm" placeholder="Min. Similarity Percentage">
					</div>
					<div class="col-4">
						<button type="button" class="btn btn-primary btn-sm" onclick="tableReload()">Apply</button>
					</div>
				</div>
			</div>
			<div class="card-body">
				<div class="table-responsive">
					<table id="mytable" class="table table-bordered table-hover table-sm text-nowrap" width="100%">
						<thead>
							<tr>
								<th width="50px">No</th>
								<th><?=lang('Title') ?></th>
								<th><?=lang('Member') ?></th>
								<th><?=lang('Total Similar') ?></th>
								<th width="50px">#</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
			<!-- /.card-body -->
		</div>
		<!-- /.card -->
	</div>
	<!-- /.container-fluid -->
</section>
<!-- /.content -->

<div class="modal fade" data-backdrop="static" id="modal">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h4 class="modal-title">Detail: <span id="modal-title"></span></h4>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<div id="modal-photo" class="row mt-1">
					<div class="col-md-5">
						<div class="form-group" data-photo="preview" style="max-width: 100%; text-align: center;">
							<a href="#" target="_blank"><img class="img-fluid" src="#"></a>
						</div>
					</div>
					<div class="col-md-7">
						<div class="form-group">
							<label>Title</label>
							<div class="form-control" data-photo="title" readonly></div>
						</div>
						<div class="form-group">
							<label>Author</label>
							<div class="form-control" data-photo="author" readonly></div>
						</div>
						<div class="form-group">
							<label>FP Number</label>
							<div class="form-control" data-photo="fpnumber" readonly></div>
						</div>
					</div>
				</div>
				<div class="table-responsive">
					<table class="table table-bordered mt-1" id="modal-similar-body">
						<thead>
							<tr>
								<th colspan="3">Similar List</th>
							</tr>
						</thead>
						<tbody></tbody>
					</table>
				</div>
			</div>
			<!--/.modal-body-->
			<div class="modal-footer justify-content-between">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>
@endsection

@section('js')
<script>
let url_ajax = '<?=$url_ajax ?>';
let tb = '#mytable';
let tb_url = url_ajax + '/datatable';
let mytable = $(tb).DataTable({
	// dom: `<"row" <"col-sm-12 col-md-6" l><"col-sm-12 col-md-6" <"toolbar"> f>>
	// 	  <"row" <"col-sm-12" tr>>
	// 	  <"row" <"col-sm-12 col-md-5" i><"col-sm-12 col-md-7" p>>`,
	processing: false,
	serverSide: true,
	ajax: {
		url: tb_url,
		type: 'GET',
		data: function(d) {
			return $.extend({}, d, {
				filter_similarity: $('#filter-similarity').val(),
			})
		},
		dataType: 'json',
		beforeSend: function() {
			$('#section-page').find('.card').append('<div class="overlay"><i class="fas fa-2x fa-sync-alt fa-spin"></i></div>');
		},
		error: function(xhr, status, error) {
			$('#section-page').find('.overlay').remove();
			alert(error);
		},
		dataSrc: function(json) {
			$('#section-page').find('.overlay').remove();
			return json.data;
		},
	},
	columnDefs: [{
		targets: '_all',
		render: $.fn.dataTable.render.text()
	}],
	columns: [
		{
			data: 'simfoni_id',
			render: function(data, type, row, meta) {
				return meta.row + meta.settings._iDisplayStart + 1;
			}
		},
		{ data: 'title' },
		{ data: 'member_name' },
		{
			data: 'total_similar',
			searchable: false,
		},
		{
			data: 'time_updated',
			orderable: false,
			searchable: false,
			className: 'text-center',
			render: function(data, type, row) {
				return `
				<div class="mb-0"><img class="img-fluid" loading="lazy" style="max-width: 50px" src="${row.image_url}"></div>
				<button type="button" class="btn btn-xs btn-info" onclick="javascript:modalDetail('${row.simfoni_id}')"><?=lang('Detail') ?></button>
				`;
			}
		}
	],
	initComplete: function() {
		var api = this.api();
		$(tb + '_filter input').off('.DT').on('keyup.DT', function(e) {
			if (e.keyCode == 13) {
				api.search(this.value).draw();
			}
		});

		let searchParams = new URLSearchParams(window.location.search);
		let param = searchParams.get('search');
		console.log('search: '+param)
		if (param != '' && param != null) {
			var e = jQuery.Event("keyup");
			e.which = 13; //choose the one you want
			e.keyCode = 13;
			$(tb+'_filter input').val(param);
			$(tb+'_filter input').trigger(e);
		}
	},
	<?php /*https://datatables.net/forums/discussion/36595/change-the-row-color-based-on-column-data*/ ?>
	createdRow: function(row, data, dataIndex) {
		//check similarity
		if (data.similarity != null && data.similarity != 'checked') {
			$(row).css('background','#ffc107').attr('title','<?=translate('Similarity Found') ?>');
		}
		if (data.disqualification != null && data.disqualification != '') {
			$(row).css('background','red').attr('title','<?=translate('Disqualified') ?>: '+data.disqualification);
		}
	}
});
</script>

<script>
function tableReload() {
	mytable.ajax.reload();
}
</script>

<script>
function modalDetail(simfoni_photo_id) {
	let url = url_ajax +'/'+simfoni_photo_id;
	let modal = $('#modal');

	//show loading
	$('#section-page').find('.card').append('<div class="overlay"><i class="fas fa-2x fa-sync-alt fa-spin"></i></div>');

	//ajax request
	$.ajax({
		url: url,
		dataType: 'json',
		success: function(response, status, xhr) {
			let data = response.data;

			//set modal title
			modal.find('.modal-title').text('Detail: '+data.title);

			//set data
			modal.find('[data-photo=preview] a').attr('href', data.image_url);
			modal.find('[data-photo=preview] img').attr('src', data.image_url);
			modal.find('[data-photo=title]').text(data.title);
			modal.find('[data-photo=author]').text(data.member_name);
			modal.find('[data-photo=fpnumber]').text(data.simfoni_member_id);

			//set similar list
			modal.find('#modal-similar-body tbody').html('<tr><td colspan="3">No Similarity</td></tr>');
			if (data.list_similar.length > 0) {
				let htmlTableSimilar = '';
				let htmlTableSimilarSamePerson = '';
				let htmlTableSimilarOtherPerson = '';
				data.list_similar.forEach(function(k, i) {
					let detail = JSON.parse(k.data).detail;
					console.log(detail)


					//collect same person
					if (data.simfoni_member_id == k.simfoni_member_id) {
						htmlTableSimilarSamePerson += `
							<tr>
								<td class="text-center" rowspan="4">
									<a href="${k.image_url}" target="_blank">
										<img src="${k.image_url}" class="img-fluid" style="max-width: 200px;">
									</a>
									<br>
									(<?=lang('Similarity') ?> <b>${k.similarity}%</b>)
								</td>
								<td>Title</td>
								<td>${detail.title}</td>
							</tr>
							<tr>
								<td>Author</td>
								<td>${detail.user.name}   <b>[${k.simfoni_member_id}]</b></td>
							</tr>
							<tr>
								<td>Event</td>
								<td>${detail.event.name} [${detail.event.year}]</td>
							</tr>
							<tr>
								<td>Category</td>
								<td>${detail.category}</td>
							</tr>
						`;
					} else {
						htmlTableSimilarOtherPerson += `
							<tr>
								<td class="text-center" rowspan="4">
									<a href="${k.image_url}" target="_blank">
										<img src="${k.image_url}" class="img-fluid" style="max-width: 200px;">
									</a>
									<br>
									(<?=lang('Similarity') ?> <b>${k.similarity}%</b>)
								</td>
								<td>Title</td>
								<td>${detail.title}</td>
							</tr>
							<tr>
								<td>Author</td>
								<td>${detail.user.name}   <b>[${k.simfoni_member_id}]</b></td>
							</tr>
							<tr>
								<td>Event</td>
								<td>${detail.event.name} [${detail.event.year}]</td>
							</tr>
							<tr>
								<td>Category</td>
								<td>${detail.category}</td>
							</tr>
						`;
					}
				});

				if (htmlTableSimilarSamePerson != '') {
					htmlTableSimilar += `
						<tr>
							<td colspan="1" class="text-bold">Similar List Same Person</td>
							<td colspan="2" class="text-bold">Info</td>
						</tr>
					`;
					htmlTableSimilar += htmlTableSimilarSamePerson;
				}
				if (htmlTableSimilarOtherPerson != '') {
					htmlTableSimilar += `
						<tr>
							<td colspan="1" class="text-bold">Similar List Other Person</td>
							<td colspan="2" class="text-bold">Info</td>
						</tr>
					`;
					htmlTableSimilar += htmlTableSimilarOtherPerson;
				}
				modal.find('#modal-similar-body tbody').html(htmlTableSimilar);
			}


			//show modal
			modal.modal('show');
		},
		error: function(xhr, status, error) {
			let response = xhr.responseJSON;
			let response_status = xhr.status;

			//handle no internet
			if (response_status == 0) {
				Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Your connection might be unstable, please try again later.') ?> ['+response_status+']', 'warning');
				return;
			}

			//handle server error
			if (response_status >= 500) {
				Swal.fire(error+' ['+response_status+']', '<?=lang('Please try again or contact our support') ?>!', 'error');
				return;
			}

			//handle server invalid response
			if (response == undefined) {
				Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Server Error') ?> ['+xhr.status+']', 'error');
				return;
			}

			//handle error response
			Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
		}
	}).always(function() {
		$('#section-page').find('.overlay').remove();
	})
}
</script>

<script>
$(document).ready(function() {
	updateProgressFromStatus();
})

function updateProgressFromStatus() {
	$.getJSON('<?=site_url("similarity/status")?>', function(response) {
		let status = response.data.status;
		let progress_url = response.data.progress_url;
		if (status) {
			$('#check-similarity').addClass('d-none');
			$('#progress').removeClass('d-none');
			sseProgress(progress_url, 'Similarity Checked ', function() {
				mytable.ajax.reload(null, false);
			});		
		}
	})
}

function sseProgress(progress_url, label, onComplete) {
	// Create new event, the server script is sse
	let eventSource = new EventSource(progress_url);

	// Event when receiving a message from the server
	let progress = 0;
	eventSource.onmessage = function(event) {
		console.log(event.data);

		let response = JSON.parse(event.data);
		if (response.code == 'error') {
			eventSource.close();

			Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
			return;
		}

		//set progress percentage
		progress = response.data.percentage;
		let progress_percentage = progress + '%';

		//set progress bar
		$('#progress-bar').width(progress_percentage);
		$('#progress-text').text(label + progress_percentage);
		if (progress >= 100) {
			console.log('progress '+label+' done');
			eventSource.close();

			// Jalankan callback setelah SSE selesai
			if (typeof onComplete === 'function') {
				onComplete();
			}
		}
	}
}

function checkSimilarity() {
	Swal.fire({
		title: 'Check Photo Similarity?',
		text: 'It will take a long time',
		icon: 'question',
		showCancelButton: true,
		focusCancel: true,
		cancelButtonColor: '#d33',
		cancelButtonText: '<?=lang('Cancel') ?>',
		confirmButtonText: '<?=lang('Check It') ?>!',
		confirmButtonColor: '#3085d6',
		showLoaderOnConfirm: true,
		allowOutsideClick: () => !Swal.isLoading(),
		preConfirm: function() {
			return new Promise(function(resolve) {
				$.ajax({
					url: '<?= site_url("similarity") ?>',
					method: 'GET',
					success: function(response, status, xhr) {
						const statusUrl = response.data.progress_url;
						$('#check-similarity').addClass('d-none');

						$('#progress').removeClass('d-none');
						$('#progress-bar').width('0%');
						$('#progress-text').text('Waiting to start...');
						setTimeout(updateProgressFromStatus, 5000);
					},
					error: function(xhr, status, error) {
						let response = xhr.responseJSON;
						let response_status = xhr.status;

						//handle no internet
						if (response_status == 0) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Your connection might be unstable, please try again later.') ?> ['+response_status+']', 'warning');
							return;
						}

						//handle server error
						if (response_status >= 500) {
							Swal.fire(error+' ['+response_status+']', '<?=lang('Please try again or contact our support') ?>!', 'error');
							return;
						}

						//handle server invalid response
						if (response == undefined) {
							Swal.fire('<?=lang('Oops') ?>..!', '<?=lang('Server Error') ?> ['+xhr.status+']', 'error');
							return;
						}

						//handle error response
						Swal.fire('<?=lang('Oops') ?>..!', response.message, 'error');
					}
				}).always(function() {
					resolve();
				});
			});
			//end promise
		}
	})
}
</script>
@endsection