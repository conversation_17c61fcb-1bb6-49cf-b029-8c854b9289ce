<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * This code is use to override 404 page where issued with `show_404()` function.
 * Reference:
 * https://stackoverflow.com/questions/39667275/codeigniter-3-show-404-function-issue-my-exception-doesnt-load
 */

class MY_Exceptions extends CI_Exceptions {

	public function show_404($page = '', $log_error = TRUE)
	{
		$CI =& get_instance();
		// $CI->load->view('layouts/404_read_view');

		$CI->load->module('layouts/Page_custom');
		$CI->page_custom->error_404();
		echo $CI->output->get_output();
		exit;
	}

}

/* End of file MY_Exceptions.php */
/* Location: ./application/core/MY_Exceptions.php */