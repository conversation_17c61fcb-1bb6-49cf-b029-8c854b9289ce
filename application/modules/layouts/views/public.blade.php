<?php
$theme = 'https://adminlte.io/themes/v3/';
$app_logo = base_url('logo-fpsi.png');
$app_name = 'SIMFONI Photo Similarity Checker';
$app_url = site_url();
$app_lang_config = $this->config->item('language_cookie_name');
$app_lang = $this->input->cookie($app_lang_config) ?? 'us';
if ($app_lang == 'en') {
	$app_lang = 'us';
}
?>
<!DOCTYPE html>
<!--
<?=$app_name . "\n" ?>
powered by: UNIQDEV
dev: kangsunu
-->
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>@yield('page_title')</title>

<!-- Google Font: Source Sans Pro -->
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
<!-- Font Awesome Icons -->
<link rel="stylesheet" href="<?=$theme?>plugins/fontawesome-free/css/all.min.css">
<!-- Flag -->
<link rel="stylesheet" href="<?=$theme?>plugins/flag-icon-css/css/flag-icon.min.css">
<!-- icheck bootstrap -->
<link rel="stylesheet" href="<?=$theme?>plugins/icheck-bootstrap/icheck-bootstrap.min.css">
<!-- SweetAlert2 -->
<link rel="stylesheet" href="<?=$theme?>plugins/sweetalert2/sweetalert2.min.css">
<!-- Theme style -->
<link rel="stylesheet" href="<?=$theme?>dist/css/adminlte.min.css">
<!-- Override style -->
<style>
.wrapper > .content-wrapper { padding-bottom: 1px; }
.wrapper > .content-wrapper .content-header { padding: 15px 0 }
.wrapper > .content-wrapper > .content-wrapper2 { max-width: 1140px; margin: auto; padding-top: 0.1px;}
</style>

@yield('css')

</head>
<body class="hold-transition layout-top-nav layout-fixed">
<div class="wrapper">

	<!-- Navbar -->
	<nav class="main-header navbar navbar-expand-md navbar-light navbar-white">
		<div class="container">
			<a href="<?=$app_url ?>" class="navbar-brand">
				<img src="<?=$app_logo ?>" alt="<?=htmlentities($app_name) ?> Logo" class="brand-image">
				<span class="brand-text font-weight-light"><?=htmlentities($app_name) ?></span>
			</a>

			<!-- Right navbar links -->
			<ul class="order-1 order-md-3 navbar-nav navbar-no-expand ml-auto">

				<!-- Select Language -->
				<li class="nav-item dropdown">
					<a class="nav-link" data-toggle="dropdown" href="javascrip:void()">
						<i class="flag-icon flag-icon-<?=$app_lang ?>"></i>
					</a>
					<div class="dropdown-menu dropdown-menu-right p-0">
						<a href="javascript:changeLanguage('en')" class="dropdown-item">
							<i class="flag-icon flag-icon-us mr-2"></i> English
						</a>
						<a href="javascript:changeLanguage('id')" class="dropdown-item">
							<i class="flag-icon flag-icon-id mr-2"></i> Indonesia
						</a>
					</div>
				</li>

				<!-- User Dropdown Menu -->
				<li class="nav-item dropdown">
					<a class="nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown">
						<i class="fas fa-user"></i>
					</a>
					<ul class="dropdown-menu dropdown-menu-right border-0 shadow">
						<li><a class="dropdown-item" href="<?=$app_url.'auth/login' ?>"><?=lang('Login') ?></a></li>
						<li><a class="dropdown-item" href="<?=$app_url.'auth/register' ?>"><?=lang('Register') ?></a></li>
						<li class="dropdown-divider"></li>
						<li><a class="dropdown-item" href="<?=$app_url.'auth/forgot' ?>"><?=lang('Forgot Password') ?></a></li>
						<li><a class="dropdown-item" href="<?=$app_url.'auth/activation/resend' ?>"><?=lang('Resend Activation') ?></a></li>
					</ul>
				</li>
			</ul>
		</div>
	</nav>
	<!-- /.navbar -->

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<div class="content-wrapper2">
			@yield('content')
		</div>
	</div>
	<!-- /.content-wrapper -->

	<!-- Main Footer -->
	<footer class="main-footer text-sm">
		<div class="container">
			<!-- To the right -->
			<div class="float-right d-none d-sm-inline">
				Powered by <a href="https://uniq.id" target="_blank"><b>UNIQDEV</b></a>
			</div>
			<!-- Default to the left -->
			<strong><a href="<?=site_url() ?>">SIMFONI Photo Similarity Checker</a> &copy; 2025.</strong> All rights reserved.
		</div>
	</footer>
</div>
<!-- ./wrapper -->

<!-- REQUIRED SCRIPTS -->
<!-- jQuery -->
<script src="<?=$theme?>plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="<?=$theme?>plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- SweetAlert2 -->
<script src="<?=$theme?>plugins/sweetalert2/sweetalert2.min.js"></script>
<!-- AdminLTE App -->
<script src="<?=$theme?>dist/js/adminlte.min.js"></script>
<!-- Set Language -->
<script>
async function setCookie(cname, cvalue, extime) {
	const d = new Date();
	d.setTime(d.getTime() + (extime*1000));
	let expires = "expires="+ d.toUTCString();
	document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}
async function changeLanguage(lang){
	document.write('<?=lang('Loading') ?>...');
	var languageKey = '<?=$app_lang_config ?>';
	var expire = 7200;
	setCookie(languageKey, lang, expire);

	window.location = window.location.href;
}
</script>

@yield('js')

</body>
</html>
