@extends('layouts/public')
@section('page_title', $page_title)
@section('content')
<div class="login-box m-auto pt-4 pb-5">
	<div class="login-logo">
		<a href="javascript:void(0)"><b>Admin</b>LTE</a>
	</div>
	<div class="card">
		<div class="card-body login-card-body">
			<p class="login-box-msg"><?=lang('Click this button to continue') ?></p>

			<form id="myform" action="<?=$url_form ?>" method="post">
				<input type="hidden" name="user_id" value="<?=$user_id ?>">
				<input type="hidden" name="code_key" value="<?=$code_key ?>">

				<div class="row">
					<div class="col-12">
						<button type="submit" class="btn btn-primary btn-block"><?=lang('Activate Account') ?></button>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
@endsection
@section('js')
<script>
$('#myform').submit(function(e) {
	e.preventDefault();

	let form = $(this);
	let url = form.attr('action');
	let method = form.attr('method');
	let btn = form.find('[type=submit]');
	let errors = form.find('[data-error]');

	$.ajax({
		url: url,
		type: method,
		data: form.serialize(),
		dataType: 'json',
		beforeSend: function() {
			//set loading
			btn.prop('disabled', true).button('loading');

			//reset form error
			form.find('[name]').removeClass('is-invalid');
			errors.text('');
		},
		success: function(response, status, xhr) {
			Swal.fire('<?=lang('Congratulation') ?>!', response.message, 'success').then(function() {
				document.write('<?=lang('Please wait') ?>..');
				window.location = '<?=site_url('auth/login') ?>';
			});
		},
		error: function(xhr, status, error) {
			let response = xhr.responseJSON;
			let response_status = xhr.status;

			//handle server error
			if (response_status >= 500) {
				Swal.fire(`<?=lang('Please try again') ?>!`,  `${error} [${response_status}]`, `error`);
				return;
			}

			//handle unwanted response
			if (response == undefined) {
				Swal.fire(`<?=lang('Oops') ?>..!`, `<?=lang('Invalid Response') ?> [${response_status}]`, `error`);
				return;
			}

			//handle error list
			Swal.fire(`<?=lang('Oops') ?>..!`, response.message, 'error');
			let errors = response.errors;
			if (errors != undefined) {
				Object.keys(errors).forEach(function(k) {
					form.find('[name='+k+']').addClass('is-invalid');
					form.find('[data-error='+k+']').addClass('text-danger text-xs').text(errors[k]);
				})
			}
		}
	}).always(function() {
		btn.prop('disabled', false).button('reset');
	});
})
</script>
@endsection