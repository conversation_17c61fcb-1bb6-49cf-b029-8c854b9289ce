<?php
function generateUUIDv4() {
	$data = random_bytes(16);

	// Set version to 0100
	$data[6] = chr((ord($data[6]) & 0x0f) | 0x40);
	// Set bits 6-7 to 10
	$data[8] = chr((ord($data[8]) & 0x3f) | 0x80);

	return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
}
function finish_request()
{
	if (function_exists('fastcgi_finish_request')) {
		fastcgi_finish_request();
	} else {
		if (php_sapi_name() == 'cli') {
			print_log('fastcgi_finish_request not available on CLI request');
		}
	}
}

function remove_suffix($string, $suffix='')
{
	if (str_ends_with($string, $suffix)) {
		$string = substr($string, 0, -strlen($suffix));
	}
	return $string;
}