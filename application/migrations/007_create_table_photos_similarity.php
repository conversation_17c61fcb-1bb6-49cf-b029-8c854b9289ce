<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_table_photos_similarity extends CI_Migration {

	protected $table = 'photos_similar';

	public function __construct()
	{
		$this->load->dbforge();
		$this->load->database();
	}

	public function up()
	{
		$fields = [
			'id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => TRUE,
				'auto_increment' => TRUE,
			],
			'simfoni_photo_id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => TRUE,
				'comment' => 'simfoni `foto_id`'
			],
			'similarity' => [
				'type' => 'FLOAT',
				'unsigned' => TRUE,
				'default' => 0,
				'comment' => 'similarity percentage'
			],
			'similar_simfoni_photo_id' => [
				'type' => 'BIGINT',
				'constraint' => 20,
				'unsigned' => TRUE,
				'comment' => 'simfoni `foto_id`'
			],
			'data' => [
				'type' => 'LONGTEXT',
				'null' => TRUE,
				'default' => NULL,
				'comment' => 'additional info (json)'
			],
			'cluster_id' => [
				'type' => 'VARCHAR',
				'constraint' => 10,
				'null' => TRUE,
				'default' => TRUE,
				'comment' => 'API service grouping'
			],
			'time_updated' => [
				'type' => 'INT',
				'constraint' => 11,
				'unsigned' => TRUE,
				'null' => FALSE,
				'default' => 0,
			],
		];
		$this->dbforge->add_field($fields);
		$this->dbforge->add_key('id', TRUE);
		$this->dbforge->add_key('simfoni_photo_id');
		$this->dbforge->create_table($this->table, true);
	}

	public function down()
	{
		$this->dbforge->drop_table($this->table, TRUE);
	}

}

/* End of file 007_create_table_photos_similarity.php */
/* Location: ./application/migrations/007_create_table_photos_similarity.php */