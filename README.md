# A PHP Application Project Starter with CodeIgniter 3

## Features
- Security Improvement, with changing index.php (root document) to public directory
- Dynamic Base URL
- [HMVC / Modular Extensions](https://github.com/sunuazizrahayu/CodeIgniter-HMVC-Modular-Extension)
- [Migration](https://github.com/sunuazizrahayu/CodeIgniter-Migration-CLI)
- Config with ENVIRONMENT variables
- Blade Template Engine, with [Slice-Library](https://github.com/GustMartins/Slice-Library)
- Send email with background process
- Storage Library (upload file)
- Multi Language & Auto Translate

## Requirements
- PHP 7.3 <= 8.0.x
- Composer

## WARNING & Tips
- All Controller must be use `MY_Controller`
- Make sure `public/storage` have chown `www-data:www-data`
